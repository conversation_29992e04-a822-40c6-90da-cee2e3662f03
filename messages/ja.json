{"common": {"contactUs": "お問い合わせ", "services": "サービス", "about": "会社概要", "home": "ホーム", "learnMore": "詳しく見る", "loading": "読み込み中...", "submit": "送信", "submitting": "送信中..."}, "navigation": {"home": "ホーム", "services": "サービス", "about": "会社概要", "contact": "お問い合わせ"}, "home": {"hero": {"title": "効率的でエレガントなデジタルソリューションの創造", "subtitle": "Sanvaソフトウェア開発スタジオは、企業や個人向けに専門的なアプリ開発、ミニプログラム開発、バックエンド開発サービスを提供することに特化しています。", "cta": "お問い合わせ"}, "services": {"title": "私たちのサービス", "subtitle": "あらゆるデジタルニーズに対応する包括的なソフトウェア開発ソリューション", "appDev": {"title": "アプリ開発", "description": "React Native、Flutter、その他の技術スタックを使用したネイティブiOS/Androidまたはクロスプラットフォームアプリ開発"}, "miniProgram": {"title": "ミニプログラム開発", "description": "WeChatミニプログラム、Alipayミニプログラム、TikTokミニプログラムなど、迅速なデプロイメントと優れたユーザーエクスペリエンスを重視"}, "backend": {"title": "バックエンド開発", "description": "RESTful API、データベース設計、クラウドサービス（AWS、Alibaba Cloudなど）ソリューション"}, "webDev": {"title": "Web開発", "description": "レスポンシブWebサイト、企業サイト、マルチデバイスアクセス対応のEコマースプラットフォーム"}, "globalPlatforms": {"title": "グローバルプラットフォーム開発", "description": "Google Playアプリ、App Storeアプリ、Facebookミニゲーム、WhatsApp Businessなどのグローバルプラットフォーム開発"}}, "testimonials": {"title": "お客様の声", "subtitle": "私たちのサービスに対するお客様の評価"}, "quickNav": {"title": "私たちについてもっと知る"}}, "services": {"title": "私たちのサービス", "subtitle": "アプリ開発、ミニプログラム開発、バックエンド開発を含む専門的なソフトウェア開発サービスを提供しています", "sectionTitle": "包括的なソフトウェア開発ソリューション", "sectionSubtitle": "モバイルアプリ、ミニプログラム、バックエンドシステムが必要な場合でも、お客様のビジネスのデジタル変革を支援する質の高い開発サービスを提供します。", "appDevelopment": {"title": "アプリ開発", "description": "最新技術を使用したネイティブiOSおよびAndroidアプリ開発サービス、またはクロスプラットフォームアプリ開発サービスを提供します。", "features": {"native": "ネイティブアプリ開発 (iOS/Android)", "crossPlatform": "クロスプラットフォームアプリ開発", "uiUx": "UI/UXデザイン", "maintenance": "アプリメンテナンス＆アップデート", "storeSupport": "アプリストア申請サポート"}}, "miniProgram": {"title": "ミニプログラム開発", "description": "WeChat、Alipay、TikTokミニプログラムなどの各種ミニプログラム開発に焦点を当て、企業が迅速にユーザーにリーチできるよう支援します。", "features": {"wechat": "WeChatミニプログラム開発", "alipay": "Alipayミニプログラム開発", "douyin": "TikTokミニプログラム開発", "multiPlatform": "マルチプラットフォームミニプログラムソリューション", "ecommerce": "ミニプログラムEコマース＆決済統合"}}, "backend": {"title": "バックエンド開発", "description": "アプリケーションに強力なデータサポートを提供する、信頼性が高く、安全で、高性能なバックエンド開発サービスを提供します。", "features": {"api": "RESTful API開発", "database": "データベース設計＆最適化", "auth": "ユーザー認証＆認可", "cloud": "クラウドサービス統合 (AWS、Alibaba Cloudなど)", "server": "サーバー設定＆メンテナンス"}}, "globalPlatforms": {"title": "グローバルプラットフォーム開発", "description": "世界中のユーザーに製品を届けるためのグローバルプラットフォーム向け開発サービスを提供しています。", "features": {"googlePlay": "Google Playアプリ開発", "appStore": "App Storeアプリ開発", "facebook": "Facebookミニゲーム開発", "whatsapp": "WhatsApp Business連携", "telegram": "Telegramボット開発", "instagram": "Instagram API連携", "twitter": "Twitter/X API連携", "linkedin": "LinkedInアプリ開発"}}, "process": {"title": "私たちの開発プロセス", "subtitle": "透明で効率的な開発プロセスにより、スムーズなプロジェクト進行と適時な納品を保証", "steps": {"analysis": {"title": "要件分析", "description": "お客様のビジネスニーズを深く理解し、プロジェクトの目標と機能範囲を定義"}, "design": {"title": "設計＆企画", "description": "技術ソリューションと設計プロトタイプを作成し、最適なユーザーエクスペリエンスを確保"}, "development": {"title": "開発実装", "description": "設計計画に従って開発を行い、定期的に進捗と成果を報告"}, "delivery": {"title": "テスト＆納品", "description": "アプリケーション機能の包括的なテストを行い、品質確保後にデプロイと納品を実施"}}}}, "contact": {"title": "お問い合わせ", "subtitle": "お客様のニーズをお聞きし、いつでも専門的なサポートを提供することを楽しみにしています", "methods": {"title": "連絡先情報", "email": "メール", "workTime": "営業時間", "workHours": "月曜日から金曜日 9:00 - 18:00"}, "followUs": "フォローする", "form": {"title": "メッセージを送信", "subtitle": "お客様のメッセージをお待ちしています。専門チームがお客様に合わせたソリューションを提供いたします", "name": "お名前", "email": "メール", "phone": "電話番号（任意）", "message": "要件の説明", "required": "*", "send": "�� 今すぐ送信"}}, "about": {"title": "会社概要", "subtitle": "Sanvaソフトウェア開発スタジオの背景と専門能力について", "introduction": {"title": "スタジオ紹介", "paragraphs": {"first": "Sanvaソフトウェア開発スタジオは2023年に設立され、企業や個人に高品質なソフトウェアソリューションを提供することに特化したチームです。私たちのサービス範囲は、モバイルアプリ開発、ミニプログラム開発、バックエンドシステム開発をカバーしています。", "second": "私たちのチームは経験豊富な開発エンジニアとデザイナーで構成されており、それぞれが堅実な技術力と革新的な思考を持っています。私たちは最先端の技術開発に注力し、継続的に新技術を学び適用して、クライアントに最高品質のサービスを提供することを保証しています。", "third": "Sanvaソフトウェアでは、技術は人々に奉仕し、企業に価値を創造すべきだと信じています。私たちは技術で実際の問題を解決し、クライアントのデジタル変革を支援し、運営効率を向上させ、市場競争力を強化することにコミットしています。"}}, "values": {"title": "私たちの核となる価値観", "subtitle": "これらの価値観が私たちの日常業務を指導し、クライアントに最高品質のサービスを提供するのに役立っています", "items": {"professional": {"title": "プロフェッショナル", "description": "私たちは豊富な技術経験と業界知識を持ち、クライアントにプロフェッショナルなソフトウェアソリューションを提供します。"}, "efficient": {"title": "効率的", "description": "私たちは開発効率とプロジェクト進行管理に焦点を当て、高品質な製品の適時納期を保証します。"}, "customerFirst": {"title": "顧客第一", "description": "私たちは顧客のニーズに中心を置き、個別化されたソリューションと心配りのあるアフターサービスを提供します。"}}}, "team": {"title": "私たちのチーム", "subtitle": "経験豊富な専門家で構成され、最高品質のサービスを提供することに専念しています", "members": {"founder": {"name": "張 太郎", "role": "創設者 / リード開発者", "description": "10年のソフトウェア開発経験を持ち、モバイルアプリとミニプログラム開発を専門としています。"}, "designer": {"name": "李 花子", "role": "UI/UXデザイナー", "description": "美しくユーザーフレンドリーなインターフェースの作成に熟練し、ユーザーエクスペリエンスのあらゆる詳細に焦点を当てています。"}, "backend": {"name": "王 次郎", "role": "バックエンド開発者", "description": "クラウドサービスとデータベース設計のエキスパートで、安定で効率的なバックエンドシステムを構築しています。"}}}}, "portfolio": {"viewDetails": "詳細を見る", "coreFeatures": "主な機能："}}