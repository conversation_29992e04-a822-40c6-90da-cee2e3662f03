{"common": {"contactUs": "Contáctanos", "services": "<PERSON><PERSON><PERSON>", "about": "Acerca de", "home": "<PERSON><PERSON>o", "learnMore": "Saber más", "loading": "Cargando...", "submit": "Enviar", "submitting": "Enviando..."}, "navigation": {"home": "<PERSON><PERSON>o", "services": "<PERSON><PERSON><PERSON>", "about": "Sobre nosotros", "contact": "Contacto"}, "home": {"hero": {"title": "Creando soluciones digitales eficientes y elegantes", "subtitle": "Sanva Software Development Studio se especializa en brindar servicios profesionales de desarrollo de aplicaciones, mini-programas y desarrollo backend para empresas e individuos.", "cta": "Contáctanos"}, "services": {"title": "Nuestros servicios", "subtitle": "Soluciones integrales de desarrollo de software para satisfacer todas sus necesidades digitales", "appDev": {"title": "Desarrollo de aplicaciones", "description": "Desarrollo de aplicaciones nativas iOS/Android o multiplataforma utilizando React Native, Flutter y otros stacks tecnológicos"}, "miniProgram": {"title": "Desarrollo de mini-programas", "description": "Mini-programas de WeChat, Alipay, TikTok y más con despliegue rápido y excelente experiencia de usuario"}, "backend": {"title": "Desarrollo backend", "description": "APIs RESTful, diseño de bases de datos, servicios en la nube (AWS, Alibaba Cloud, etc.)"}, "webDev": {"title": "Desarrollo web", "description": "Sitios web responsivos, sitios corporativos, plataformas de comercio electrónico con acceso multidispositivo"}, "globalPlatforms": {"title": "Desarrollo de plataformas globales", "description": "Aplicaciones de Google Play, App Store, mini-juegos de Facebook, WhatsApp Business y otras plataformas globales"}}, "testimonials": {"title": "Testimonios de clientes", "subtitle": "Lo que nuestros clientes dicen sobre nuestros servicios"}, "quickNav": {"title": "Conoce más sobre nosotros"}}, "services": {"title": "Nuestros servicios", "subtitle": "Ofrecemos servicios profesionales de desarrollo de software incluyendo desarrollo de aplicaciones, mini-programas y backend", "sectionTitle": "Soluciones integrales de desarrollo de software", "sectionSubtitle": "Ya sea que necesites aplicaciones móviles, mini-programas o sistemas backend, ofrecemos servicios de desarrollo de calidad para ayudar a tu negocio lograr la transformación digital.", "appDevelopment": {"title": "Desarrollo de aplicaciones", "description": "Ofrecemos servicios de desarrollo de aplicaciones nativas para iOS y Android, así como desarrollo de aplicaciones multiplataforma usando tecnologías modernas.", "features": {"native": "Desarrollo de aplicaciones nativas (iOS/Android)", "crossPlatform": "Desarrollo de aplicaciones multiplataforma", "uiUx": "Diseño UI/UX", "maintenance": "Mantenimiento y actualizaciones de aplicaciones", "storeSupport": "Soporte para publicación en tiendas de aplicaciones"}}, "miniProgram": {"title": "Desarrollo de mini-programas", "description": "Nos enfocamos en desarrollar varios mini-programas incluyendo WeChat, Alipay y TikTok para ayudar a las empresas a llegar a los usuarios rápidamente.", "features": {"wechat": "Desarrollo de mini-programas de WeChat", "alipay": "Desarrollo de mini-programas de Alipay", "douyin": "Desarrollo de mini-programas de TikTok", "multiPlatform": "Soluciones de mini-programas multiplataforma", "ecommerce": "Integración de comercio electrónico y pagos en mini-programas"}}, "backend": {"title": "Desarrollo backend", "description": "Ofrecemos servicios de desarrollo backend confiables, seguros y de alto rendimiento para potenciar tus aplicaciones.", "features": {"api": "Desarrollo de APIs RESTful", "database": "Diseño y optimización de bases de datos", "auth": "Autenticación y autorización de usuarios", "cloud": "Integración de servicios en la nube (AWS, Alibaba Cloud, etc.)", "server": "Configuración y mantenimiento de servidores"}}, "globalPlatforms": {"title": "Desarrollo de plataformas globales", "description": "Ofrecemos servicios de desarrollo para plataformas globales para ayudar a que tus productos lleguen a usuarios de todo el mundo.", "features": {"googlePlay": "Desarrollo de aplicaciones para Google Play", "appStore": "Desarrollo de aplicaciones para App Store", "facebook": "Desarrollo de mini-juegos de Facebook", "whatsapp": "Integración de WhatsApp Business", "telegram": "Desarrollo de bots de Telegram", "instagram": "Integración de API de Instagram", "twitter": "Integración de API de Twitter/X", "linkedin": "Desarrollo de aplicaciones de LinkedIn"}}, "process": {"title": "Nuestro proceso de desarrollo", "subtitle": "Proceso de desarrollo transparente y eficiente que garantiza el progreso fluido del proyecto y la entrega oportuna", "steps": {"analysis": {"title": "Análisis de requisitos", "description": "Comprensión profunda de las necesidades de tu negocio, definiendo objetivos del proyecto y alcance de funcionalidades"}, "design": {"title": "Diseño y planificación", "description": "Creación de soluciones técnicas y prototipos de diseño para asegurar la experiencia de usuario óptima"}, "development": {"title": "Implementación del desarrollo", "description": "Desarrollo según los planes de diseño con informes regulares de progreso y entregables"}, "delivery": {"title": "Pruebas y entrega", "description": "Pruebas exhaustivas de las funcionalidades de la aplicación, asegurando la calidad antes del despliegue y entrega"}}}}, "contact": {"title": "Contáctanos", "subtitle": "Esperamos escuchar tus necesidades y brindar soporte profesional en cualquier momento", "methods": {"title": "Información de contacto", "email": "Correo electrónico", "workTime": "Ho<PERSON>io laboral", "workHours": "Lunes a viernes 9:00 - 18:00"}, "followUs": "Síguenos", "form": {"title": "<PERSON><PERSON><PERSON> men<PERSON>", "subtitle": "Esperamos tu mensaje. Nuestro equipo profesional te proporcionará soluciones personalizadas", "name": "Nombre", "email": "Correo electrónico", "phone": "Teléfono (Opcional)", "message": "Descripción de requisitos", "required": "*", "send": "💌 En<PERSON>r ahora", "success": {"title": "🎉 ¡<PERSON><PERSON><PERSON> por tu mensaje!", "message": "Hemos recibido tu mensaje. Nuestro equipo profesional se pondrá en contacto contigo en 24 horas.", "urgent": "Para necesidades urgentes, envía un correo <NAME_EMAIL>"}, "errors": {"nameRequired": "Por favor ingresa tu nombre", "emailRequired": "Por favor ingresa tu correo electrónico", "emailInvalid": "Por favor ingresa una dirección de correo válida", "messageRequired": "Por favor ingresa la descripción de tus requisitos"}}, "faq": {"title": "Preguntas frecuentes", "subtitle": "Aquí hay algunas preguntas frecuentes de nuestros clientes. Si tienes otras preguntas, no dudes en contactarnos", "questions": {"timeline": {"q": "¿Cuánto tiempo toma típicamente el desarrollo de un proyecto?", "a": "El tiempo de desarrollo del proyecto depende de la escala y complejidad del proyecto. Mini-programas simples pueden tomar 2-4 semanas, mientras que aplicaciones complejas pueden requerir 2-3 meses. Proporcionamos estimaciones de tiempo detalladas antes del inicio del proyecto."}, "pricing": {"q": "¿<PERSON><PERSON><PERSON>les son sus estándares de precios?", "a": "Determinamos los precios basados en la complejidad del proyecto, cantidad de funcionalidades y tiempo de desarrollo. Ofrecemos servicios gratuitos de evaluación y cotización de proyectos. Por favor contáctanos para información detallada."}, "maintenance": {"q": "¿Proporcionan servicios de mantenimiento después del desarrollo?", "a": "Sí, proporcionamos soporte técnico y servicios de mantenimiento después de la entrega del proyecto. Usualmente ofrecemos 1-3 meses de período de mantenimiento gratuito, seguido de contratos de mantenimiento a largo plazo."}, "modification": {"q": "¿Pueden modificar aplicaciones o mini-programas existentes?", "a": "Sí. Podemos hacernos cargo y modificar proyectos existentes, optimizar funcionalidades o solucionar problemas. Primero evaluamos el código, luego proporcionamos planes de modificación y cotizaciones."}}}}, "about": {"title": "Acerca de", "subtitle": "Conozca la historia y capacidades profesionales de Sanva Software Development Studio", "introduction": {"title": "Presentación del Estudio", "paragraphs": {"first": "Sanva Software Development Studio fue fundado en 2023, especializándose en proporcionar soluciones de software de alta calidad para empresas e individuos. Nuestros servicios cubren desarrollo de aplicaciones móviles, desarrollo de mini-programas y desarrollo de sistemas backend.", "second": "Nuestro equipo está compuesto por ingenieros de desarrollo y diseñadores experimentados, cada uno con habilidades técnicas sólidas y pensamiento innovador. Nos enfocamos en el desarrollo de tecnología de vanguardia, aprendiendo y aplicando continuamente nuevas tecnologías para asegurar que proporcionamos servicios de la más alta calidad a nuestros clientes.", "third": "En Sanva Software, creemos que la tecnología debe servir a las personas y crear valor para las empresas. Estamos comprometidos a resolver problemas reales con tecnología, ayudando a los clientes a lograr la transformación digital, mejorar la eficiencia operativa y fortalecer la competitividad del mercado."}}, "values": {"title": "Nuestros Valores Fundamentales", "subtitle": "Estos valores guían nuestro trabajo diario y nos ayudan a proporcionar servicios de la más alta calidad a nuestros clientes", "items": {"professional": {"title": "Profesional", "description": "Tenemos amplia experiencia técnica y conocimiento de la industria para proporcionar soluciones de software profesionales a nuestros clientes."}, "efficient": {"title": "<PERSON><PERSON><PERSON>", "description": "Nos enfocamos en la eficiencia del desarrollo y la gestión del progreso del proyecto para asegurar la entrega oportuna de productos de alta calidad."}, "customerFirst": {"title": "Cliente Primero", "description": "Nos centramos en las necesidades del cliente, proporcionando soluciones personalizadas y servicio postventa atento."}}}, "team": {"title": "Nuestro Equipo", "subtitle": "Compuesto por profesionales experimentados dedicados a proporcionarle servicios de la más alta calidad", "members": {"founder": {"name": "<PERSON>", "role": "Fundador / Desarrollador Principal", "description": "Con 10 años de experiencia en desarrollo de software, especializado en desarrollo de aplicaciones móviles y mini-programas."}, "designer": {"name": "<PERSON>", "role": "Diseñador UI/UX", "description": "Hábil en crear interfaces hermosas y fáciles de usar, enfocándose en cada detalle de la experiencia del usuario."}, "backend": {"name": "<PERSON>", "role": "Desarroll<PERSON>", "description": "Experto en servicios en la nube y diseño de bases de datos, construyendo sistemas backend estables y eficientes."}}}}, "portfolio": {"viewDetails": "<PERSON><PERSON> <PERSON><PERSON><PERSON>", "coreFeatures": "Características principales:"}}