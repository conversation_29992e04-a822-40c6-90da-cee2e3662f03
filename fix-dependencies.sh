#!/bin/bash

# 修复 lightningcss 依赖问题的脚本

echo "🔧 修复 Tailwind CSS 4 依赖问题..."

# 1. 清理现有依赖
echo "🧹 清理现有依赖..."
rm -rf node_modules
rm -f package-lock.json

# 2. 清理 npm 缓存
echo "🗑️ 清理 npm 缓存..."
npm cache clean --force

# 3. 重新安装依赖
echo "📦 重新安装依赖..."
npm install

# 4. 验证关键依赖
echo "✅ 验证依赖安装..."

if [ -d "node_modules/@tailwindcss" ]; then
    echo "✅ Tailwind CSS 安装成功"
else
    echo "❌ Tailwind CSS 安装失败"
    exit 1
fi

if [ -d "node_modules/lightningcss" ]; then
    echo "✅ LightningCSS 安装成功"
else
    echo "❌ LightningCSS 安装失败"
    exit 1
fi

# 5. 尝试构建
echo "🏗️ 测试构建..."
npm run build

if [ $? -eq 0 ]; then
    echo "✅ 构建成功！依赖问题已修复"
else
    echo "❌ 构建仍然失败，请检查其他问题"
    exit 1
fi

echo "🎉 所有问题已修复！"
