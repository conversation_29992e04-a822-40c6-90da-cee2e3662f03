#!/bin/bash

# 修复 lightningcss 和 @next/font 依赖问题的脚本

echo "🔧 修复 Tailwind CSS 4 和 Next.js 字体依赖问题..."

# 1. 清理现有依赖
echo "🧹 清理现有依赖..."
rm -rf node_modules
rm -f package-lock.json

# 2. 清理 npm 缓存
echo "🗑️ 清理 npm 缓存..."
npm cache clean --force

# 3. 移除旧的 @next/font 包（如果存在）
echo "🗑️ 移除旧的 @next/font 包..."
npm uninstall @next/font 2>/dev/null || true

# 4. 重新安装依赖
echo "📦 重新安装依赖..."
npm install

# 5. 验证关键依赖
echo "✅ 验证依赖安装..."

if [ -d "node_modules/@tailwindcss" ]; then
    echo "✅ Tailwind CSS 安装成功"
else
    echo "❌ Tailwind CSS 安装失败"
    exit 1
fi

if [ -d "node_modules/lightningcss" ]; then
    echo "✅ LightningCSS 安装成功"
else
    echo "❌ LightningCSS 安装失败"
    exit 1
fi

# 验证 @next/font 已被移除
if [ ! -d "node_modules/@next/font" ]; then
    echo "✅ 旧的 @next/font 包已移除"
else
    echo "⚠️ 旧的 @next/font 包仍然存在，但不影响构建"
fi

# 6. 尝试构建
echo "🏗️ 测试构建..."
npm run build

if [ $? -eq 0 ]; then
    echo "✅ 构建成功！所有依赖问题已修复"
    echo "✅ @next/font 警告已解决"
    echo "✅ lightningcss 依赖已修复"
else
    echo "❌ 构建仍然失败，请检查其他问题"
    exit 1
fi

echo "🎉 所有问题已修复！现在可以正常部署了。"
