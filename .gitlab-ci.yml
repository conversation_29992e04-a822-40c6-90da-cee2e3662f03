# GitLab CI/CD 配置文件 - 三娃软件网站自动部署

# 定义阶段
stages:
  - test
  - build
  - deploy

# 全局变量
variables:
  NODE_VERSION: "20"
  NPM_CONFIG_CACHE: "$CI_PROJECT_DIR/.npm"
  CYPRESS_CACHE_FOLDER: "$CI_PROJECT_DIR/cache/Cypress"

# 缓存配置
cache:
  paths:
    - .npm/
    - cache/Cypress/
    - node_modules/

# 测试阶段
test:
  stage: test
  image: node:${NODE_VERSION}-alpine
  before_script:
    - apk add --no-cache git
    - npm ci --cache .npm --prefer-offline
  script:
    - npm run lint
    - npm run test:e2e
  artifacts:
    when: always
    reports:
      junit:
        - test-results/junit.xml
    paths:
      - test-results/
      - playwright-report/
    expire_in: 1 week
  only:
    - merge_requests
    - main

# 构建阶段
build:
  stage: build
  image: node:${NODE_VERSION}-alpine
  before_script:
    - apk add --no-cache git
    - rm -rf node_modules package-lock.json
    - npm install
  script:
    - npm run build
  artifacts:
    paths:
      - .next/
      - public/
    expire_in: 1 hour
  only:
    - main

# 部署到生产环境
deploy_production:
  stage: deploy
  image: alpine:latest
  before_script:
    - apk add --no-cache openssh-client bash curl
    - eval $(ssh-agent -s)
    - echo "$SSH_PRIVATE_KEY" | tr -d '\r' | ssh-add -
    - mkdir -p ~/.ssh
    - chmod 700 ~/.ssh
    - ssh-keyscan -H $DEPLOY_HOST >> ~/.ssh/known_hosts
    - chmod 644 ~/.ssh/known_hosts
  script:
    - |
      ssh $DEPLOY_USER@$DEPLOY_HOST << 'EOF'
        set -e
        cd $DEPLOY_PATH
        
        echo "🔄 拉取最新代码..."
        git fetch origin
        git reset --hard origin/main
        
        echo "📦 安装依赖..."
        rm -rf node_modules package-lock.json
        npm install
        
        echo "🏗️ 构建项目..."
        npm run build
        
        echo "📁 创建日志目录..."
        mkdir -p logs
        
        echo "🚀 部署到PM2..."
        if pm2 list | grep -q "sanva-website"; then
          pm2 reload ecosystem.config.js --env production
        else
          pm2 start ecosystem.config.js --env production
        fi
        
        pm2 save
        
        echo "✅ 部署完成！"
        pm2 list
      EOF
  environment:
    name: production
    url: http://$DEPLOY_HOST:55300
  only:
    - main
  when: manual

# 部署到测试环境
deploy_staging:
  stage: deploy
  image: alpine:latest
  before_script:
    - apk add --no-cache openssh-client bash
    - eval $(ssh-agent -s)
    - echo "$SSH_PRIVATE_KEY" | tr -d '\r' | ssh-add -
    - mkdir -p ~/.ssh
    - chmod 700 ~/.ssh
    - ssh-keyscan -H $STAGING_HOST >> ~/.ssh/known_hosts
  script:
    - |
      ssh $DEPLOY_USER@$STAGING_HOST << 'EOF'
        set -e
        cd $STAGING_PATH
        
        git fetch origin
        git reset --hard origin/main
        
        rm -rf node_modules package-lock.json
        npm install
        npm run build
        
        mkdir -p logs
        
        if pm2 list | grep -q "sanva-website-staging"; then
          pm2 reload ecosystem.config.js --env staging
        else
          pm2 start ecosystem.config.js --env staging
        fi
        
        pm2 save
      EOF
  environment:
    name: staging
    url: http://$STAGING_HOST:55301
  only:
    - main
  except:
    - tags
