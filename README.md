# 三娃软件开发工作室官网 | Sanva Studio Website

一个现代化、多语言的软件开发工作室官网，采用 Next.js 15 + React 19 + TypeScript 构建。

## ✨ 最新改进 (2024年改版)

### 🎨 视觉美化
- **全新配色系统**: 采用现代化的渐变色彩和语义化颜色变量
- **专业图标库**: 集成 Lucide React 图标，替换原有的 emoji 图标
- **优化字体系统**: 使用 Inter、Poppins 和 JetBrains Mono 字体组合
- **增强动画效果**: 添加更多微交互和流畅的过渡动画
- **响应式设计**: 完善的移动端适配和跨设备体验

### 📝 内容丰富
- **项目作品集**: 新增 `/portfolio` 页面展示项目案例
- **增强服务展示**: 更详细的服务介绍和功能特性
- **客户评价优化**: 改进评价卡片设计和内容展示
- **联系表单升级**: 美化表单设计，保持原有邮件发送功能

### 🚀 功能增强
- **组件化设计**: 创建可复用的 UI 组件库 (Button, Card, Icons)
- **类型安全**: 完整的 TypeScript 类型定义
- **性能优化**: 优化动画性能和加载体验
- **SEO友好**: 保持原有的 SEO 优化和结构化数据

## 🛠 技术栈

- **框架**: Next.js 15 (App Router)
- **前端**: React 19 + TypeScript
- **样式**: Tailwind CSS 4 + 自定义 CSS 变量
- **动画**: Framer Motion
- **图标**: Lucide React
- **国际化**: next-intl (支持10种语言)
- **邮件**: Nodemailer (联系表单)

## 📦 安装和运行

### 环境要求
- Node.js 18+
- npm/yarn/pnpm

### 安装依赖
```bash
npm install
# 或
yarn install
```

### 环境配置
复制环境变量示例文件：
```bash
cp .env.example .env.local
```

编辑 `.env.local` 文件，配置邮件服务：
```env
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password
ADMIN_EMAIL=<EMAIL>
```

### 启动开发服务器
```bash
npm run dev
# 或
yarn dev
```

访问 [http://localhost:3000](http://localhost:3000) 查看网站。

## 📁 项目结构

```
src/
├── app/                    # Next.js App Router
│   ├── [locale]/          # 多语言路由
│   │   ├── page.tsx       # 首页
│   │   ├── about/         # 关于我们
│   │   ├── services/      # 服务介绍
│   │   ├── portfolio/     # 项目作品集 (新增)
│   │   └── contact/       # 联系我们
│   ├── api/               # API 路由
│   │   └── contact/       # 联系表单 API
│   ├── globals.css        # 全局样式 (已升级)
│   └── layout.tsx         # 根布局
├── components/            # 组件库
│   ├── ui/               # UI 组件 (新增)
│   │   ├── Button.tsx    # 按钮组件
│   │   ├── Card.tsx      # 卡片组件
│   │   └── Icons.tsx     # 图标组件
│   ├── Header.tsx        # 头部导航 (已升级)
│   └── Footer.tsx        # 页脚
├── lib/                  # 工具库
│   └── i18n.ts          # 国际化配置
└── middleware.ts         # 中间件
```

## 🌍 多语言支持

网站支持以下语言：
- 🇨🇳 中文 (zh)
- 🇺🇸 English (en)
- 🇫🇷 Français (fr)
- 🇩🇪 Deutsch (de)
- 🇯🇵 日本語 (ja)
- 🇰🇷 한국어 (ko)
- 🇪🇸 Español (es)
- 🇵🇹 Português (pt)
- 🇷🇺 Русский (ru)
- 🇮🇹 Italiano (it)

## 🎨 设计系统

### 颜色规范
- **主色**: Primary (蓝色系)
- **辅助色**: Secondary (绿色系)
- **强调色**: Accent (紫色系)
- **中性色**: Neutral (灰色系)

### 组件库
- **Button**: 多种变体和尺寸的按钮组件
- **Card**: 灵活的卡片容器组件
- **Icons**: 统一的图标组件库

## 📧 联系表单配置

联系表单支持自动发送邮件，需要配置 SMTP 服务：

1. **Gmail 配置示例**:
   - 启用两步验证
   - 生成应用专用密码
   - 使用应用密码作为 `SMTP_PASS`

2. **其他邮件服务**:
   - 根据服务商文档配置 SMTP 参数
   - 确保防火墙允许 SMTP 端口

## 🚀 部署

### Vercel 部署 (推荐)
```bash
npm run build
```

### 环境变量设置
在部署平台设置以下环境变量：
- `SMTP_HOST`
- `SMTP_PORT`
- `SMTP_USER`
- `SMTP_PASS`
- `ADMIN_EMAIL`

## 📄 许可证

MIT License - 详见 [LICENSE](LICENSE) 文件。

## 🤝 贡献

欢迎提交 Issue 和 Pull Request！

## 📞 联系我们

- 邮箱: <EMAIL>
- 网站: https://sanva.top
