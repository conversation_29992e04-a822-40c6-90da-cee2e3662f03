{"name": "site", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "test:e2e": "playwright test", "test:e2e:ui": "playwright test --ui", "deploy": "./deploy.sh", "deploy:quick": "./quick-deploy.sh", "pm2:start": "pm2 start ecosystem.config.js --env production", "pm2:reload": "pm2 reload ecosystem.config.js --env production", "pm2:stop": "pm2 stop sanva-website", "pm2:restart": "pm2 restart sanva-website", "pm2:logs": "pm2 logs sanva-website", "pm2:status": "pm2 list", "fix:deps": "./fix-dependencies.sh", "fix:font": "./fix-font.sh", "postinstall": "echo '✅ 依赖安装完成'"}, "dependencies": {"@types/nodemailer": "^6.4.17", "framer-motion": "^12.16.0", "lucide-react": "^0.540.0", "next": "15.3.3", "next-intl": "^4.1.0", "nodemailer": "^7.0.3", "react": "^19.0.0", "react-dom": "^19.0.0", "react-intersection-observer": "^9.16.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@playwright/test": "^1.54.2", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.3", "tailwindcss": "^4", "typescript": "^5"}}