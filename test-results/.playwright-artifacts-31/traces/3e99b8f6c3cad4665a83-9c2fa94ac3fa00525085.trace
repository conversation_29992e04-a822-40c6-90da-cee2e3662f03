{"version":8,"type":"context-options","origin":"library","browserName":"chromium","options":{"noDefaultViewport":false,"viewport":{"width":1280,"height":720},"ignoreHTTPSErrors":false,"javaScriptEnabled":true,"bypassCSP":false,"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.7258.5 Safari/537.36","locale":"en-US","offline":false,"deviceScaleFactor":1,"isMobile":false,"hasTouch":false,"colorScheme":"light","acceptDownloads":"accept","baseURL":"http://localhost:3000","recordVideo":{"dir":"/Users/<USER>/gitlab1/sanva_website/test-results/.playwright-artifacts-31","size":{"width":800,"height":450}},"serviceWorkers":"allow","selectorEngines":[],"testIdAttributeName":"data-testid"},"platform":"darwin","wallTime":1756193804262,"monotonicTime":2675286.926,"sdkLanguage":"javascript","testIdAttributeName":"data-testid","contextId":"browser-context@42e3c247cec9fbf4401f86114ae3dae1","title":"e2e/navigation.spec.ts:27 › Navigation tests - en › Navigation to case-studies page works"}
{"type":"before","callId":"call@6","startTime":2675292.449,"class":"BrowserContext","method":"newPage","params":{},"stepId":"pw:api@8","beforeSnapshot":"before@call@6"}
{"type":"event","time":2675386.06,"class":"BrowserContext","method":"page","params":{"pageId":"page@838b77fb9df86e90d6e4162a9261d75d"}}
{"type":"after","callId":"call@6","endTime":2675386.148,"result":{"page":"<Page>"},"afterSnapshot":"after@call@6"}
{"type":"before","callId":"call@8","startTime":2675390.625,"class":"Frame","method":"goto","params":{"url":"/en","timeout":0,"waitUntil":"load"},"stepId":"pw:api@9","pageId":"page@838b77fb9df86e90d6e4162a9261d75d","beforeSnapshot":"before@call@8"}
{"type":"frame-snapshot","snapshot":{"callId":"call@8","snapshotName":"before@call@8","pageId":"page@838b77fb9df86e90d6e4162a9261d75d","frameId":"frame@********************************","frameUrl":"about:blank","html":["HTML",{},["HEAD",{},["BASE",{"href":"about:blank"}]],["BODY"]],"viewport":{"width":1280,"height":720},"timestamp":2675392.869,"wallTime":1756193804368,"collectionTime":0.9000000357627869,"resourceOverrides":[],"isMainFrame":true}}
{"type":"log","callId":"call@8","time":2675394.2,"message":"navigating to \"http://localhost:3000/en\", waiting until \"load\""}
{"type":"screencast-frame","pageId":"page@838b77fb9df86e90d6e4162a9261d75d","sha1":"<EMAIL>","width":1280,"height":720,"timestamp":2675426.485,"frameSwapWallTime":1756193804397.202}
{"type":"before","callId":"call@10","startTime":2742685.441,"class":"Page","method":"screenshot","params":{"timeout":5000,"type":"png","caret":"initial"},"stepId":"pw:api@11","pageId":"page@838b77fb9df86e90d6e4162a9261d75d","beforeSnapshot":"before@call@10"}
{"type":"log","callId":"call@10","time":2742686.359,"message":"taking page screenshot"}
{"type":"log","callId":"call@10","time":2742686.532,"message":"waiting for fonts to load..."}
{"type":"log","callId":"call@10","time":2742686.7,"message":"fonts loaded"}
{"type":"after","callId":"call@10","endTime":2747685.443,"error":{"message":"Timeout 5000ms exceeded.","stack":"TimeoutError: Timeout 5000ms exceeded.\n    at ProgressController.run (/Users/<USER>/gitlab1/sanva_website/node_modules/playwright-core/lib/server/progress.js:65:26)\n    at PageDispatcher._runCommand (/Users/<USER>/gitlab1/sanva_website/node_modules/playwright-core/lib/server/dispatchers/dispatcher.js:91:31)\n    at DispatcherConnection.dispatch (/Users/<USER>/gitlab1/sanva_website/node_modules/playwright-core/lib/server/dispatchers/dispatcher.js:322:39)","name":"TimeoutError"},"afterSnapshot":"after@call@10"}
