{"type": "resource-snapshot", "snapshot": {"_frameref": "frame@********************************", "_monotonicTime": 2675395.576, "startedDateTime": "2025-08-26T07:36:44.371Z", "time": -1, "request": {"method": "GET", "url": "http://localhost:3000/en", "httpVersion": "HTTP/1.1", "cookies": [], "headers": [{"name": "Accept-Language", "value": "en-US"}, {"name": "Upgrade-Insecure-Requests", "value": "1"}, {"name": "User-Agent", "value": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.7258.5 Safari/537.36"}, {"name": "sec-ch-ua", "value": "\"Not;A=Brand\";v=\"99\", \"HeadlessChrome\";v=\"139\", \"Chromium\";v=\"139\""}, {"name": "sec-ch-ua-mobile", "value": "?0"}, {"name": "sec-ch-ua-platform", "value": "\"Windows\""}], "queryString": [], "headersSize": -1, "bodySize": 0}, "response": {"status": -1, "statusText": "", "httpVersion": "HTTP/1.1", "cookies": [], "headers": [], "content": {"size": -1, "mimeType": "x-unknown"}, "headersSize": -1, "bodySize": -1, "redirectURL": "", "_transferSize": -1}, "cache": {}, "timings": {"send": -1, "wait": -1, "receive": -1}, "pageref": "page@838b77fb9df86e90d6e4162a9261d75d"}}