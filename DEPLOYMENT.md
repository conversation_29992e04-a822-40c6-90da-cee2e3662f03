# 🚀 部署指南

## GitLab CI/CD 环境变量配置

在 GitLab 项目的 **Settings > CI/CD > Variables** 中添加以下环境变量：

### 必需的环境变量

| 变量名 | 描述 | 示例值 |
|--------|------|--------|
| `SSH_PRIVATE_KEY` | SSH 私钥（用于连接服务器） | `-----BEGIN OPENSSH PRIVATE KEY-----...` |
| `DEPLOY_HOST` | 生产服务器地址 | `your-server.com` |
| `DEPLOY_USER` | 服务器用户名 | `deploy` |
| `DEPLOY_PATH` | 项目部署路径 | `/var/www/sanva-website` |
| `STAGING_HOST` | 测试服务器地址（可选） | `staging.your-server.com` |
| `STAGING_PATH` | 测试环境路径（可选） | `/var/www/sanva-website-staging` |

### 应用环境变量（在服务器上配置）

在服务器的项目目录中创建 `.env.production` 文件：

```bash
# 邮件配置
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password
ADMIN_EMAIL=<EMAIL>

# 应用配置
NODE_ENV=production
PORT=55300
HOST=0.0.0.0
```

## 🔧 服务器环境准备

### 1. 安装必要软件

```bash
# 更新系统
sudo apt update && sudo apt upgrade -y

# 安装 Node.js 20
curl -fsSL https://deb.nodesource.com/setup_20.x | sudo -E bash -
sudo apt-get install -y nodejs

# 安装 PM2
sudo npm install -g pm2

# 安装 Git
sudo apt install git -y
```

### 2. 创建部署用户

```bash
# 创建部署用户
sudo adduser deploy

# 添加到 sudo 组
sudo usermod -aG sudo deploy

# 切换到部署用户
sudo su - deploy
```

### 3. 配置 SSH 密钥

```bash
# 生成 SSH 密钥对
ssh-keygen -t rsa -b 4096 -C "<EMAIL>"

# 将公钥添加到 authorized_keys
cat ~/.ssh/id_rsa.pub >> ~/.ssh/authorized_keys
chmod 600 ~/.ssh/authorized_keys

# 将私钥内容复制到 GitLab CI/CD 变量 SSH_PRIVATE_KEY
cat ~/.ssh/id_rsa
```

### 4. 克隆项目

```bash
# 创建项目目录
sudo mkdir -p /var/www/sanva-website
sudo chown deploy:deploy /var/www/sanva-website

# 克隆项目
cd /var/www
git clone https://gitlab.com/your-username/sanva_website.git sanva-website
cd sanva-website

# 设置权限
sudo chown -R deploy:deploy /var/www/sanva-website
```

## 🚀 部署流程

### 自动部署（推荐）

1. 推送代码到 `main` 分支
2. GitLab CI/CD 自动运行测试和构建
3. 在 GitLab 界面手动触发生产部署

### 手动部署

```bash
# 在服务器上执行
cd /var/www/sanva-website
./deploy.sh
```

## 📊 监控和维护

### PM2 常用命令

```bash
# 查看状态
pm2 list

# 查看日志
pm2 logs sanva-website

# 重启服务
pm2 restart sanva-website

# 停止服务
pm2 stop sanva-website

# 查看详细信息
pm2 show sanva-website

# 监控面板
pm2 monit
```

### 日志管理

```bash
# 查看应用日志
tail -f /var/www/sanva-website/logs/out.log

# 查看错误日志
tail -f /var/www/sanva-website/logs/error.log

# 清理日志
pm2 flush sanva-website
```

## 🔍 故障排除

### 常见问题

1. **构建失败 - lightningcss 错误**
   ```bash
   # 清理并重新安装依赖
   rm -rf node_modules package-lock.json
   npm install
   ```

2. **PM2 进程无法启动**
   ```bash
   # 检查端口是否被占用
   sudo netstat -tlnp | grep 55300
   
   # 杀死占用端口的进程
   sudo kill -9 <PID>
   ```

3. **权限问题**
   ```bash
   # 修复文件权限
   sudo chown -R deploy:deploy /var/www/sanva-website
   chmod +x /var/www/sanva-website/deploy.sh
   ```

## 🔐 安全建议

1. 定期更新系统和依赖
2. 使用防火墙限制端口访问
3. 定期备份数据和配置
4. 监控服务器资源使用情况
5. 设置日志轮转避免磁盘空间不足
