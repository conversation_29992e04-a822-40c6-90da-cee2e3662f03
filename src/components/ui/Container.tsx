import React from 'react';

// 语义化容器，统一版心与内边距，便于在页面中快速布局
// Confirmed via mcp-feedback-enhanced
interface ContainerProps {
  children: React.ReactNode;
  className?: string;
  as?: React.ElementType;
  size?: 'sm' | 'md' | 'lg' | 'xl' | '2xl';
}

const sizes = {
  sm: 'max-w-screen-sm',
  md: 'max-w-screen-md',
  lg: 'max-w-screen-lg',
  xl: 'max-w-screen-xl',
  '2xl': 'max-w-[1440px]',
} as const;

export default function Container({ children, className = '', as: Tag = 'div', size = 'xl' }: ContainerProps) {
  return (
    <Tag className={`container ${sizes[size]} mx-auto px-4 sm:px-6 lg:px-8 ${className}`.trim()}>
      {children}
    </Tag>
  );
}

