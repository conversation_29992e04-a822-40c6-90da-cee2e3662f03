import React from 'react';

// 统一 Badge，用于标签/技术栈等
// Confirmed via mcp-feedback-enhanced
export interface BadgeProps {
  children: React.ReactNode;
  color?: 'primary' | 'secondary' | 'accent' | 'neutral';
  soft?: boolean; // 是否使用柔和色块
  className?: string;
}

export default function Badge({ children, color = 'primary', soft = true, className = '' }: BadgeProps) {
  const base = 'inline-flex items-center rounded-full text-xs font-medium px-2.5 py-1 transition-colors duration-200';
  const map: Record<string, string> = soft ? {
    primary: 'bg-primary-50 text-primary-700 ring-1 ring-primary-100',
    secondary: 'bg-secondary-50 text-secondary-700 ring-1 ring-secondary-100',
    accent: 'bg-accent-50 text-accent-700 ring-1 ring-accent-100',
    neutral: 'bg-neutral-100 text-neutral-700 ring-1 ring-neutral-200',
  } : {
    primary: 'bg-primary-600 text-white',
    secondary: 'bg-secondary-600 text-white',
    accent: 'bg-accent-600 text-white',
    neutral: 'bg-neutral-700 text-white',
  };
  return <span className={`${base} ${map[color]} ${className}`.trim()}>{children}</span>;
}

