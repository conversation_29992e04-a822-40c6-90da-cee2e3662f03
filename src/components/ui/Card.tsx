'use client';

import React from 'react';
import { motion } from 'framer-motion';

interface CardProps {
  children: React.ReactNode;
  className?: string;
  hover?: boolean;
  gradient?: boolean;
  glass?: boolean;
  padding?: 'none' | 'sm' | 'md' | 'lg' | 'xl';
  shadow?: 'none' | 'sm' | 'md' | 'lg' | 'xl';
  rounded?: 'none' | 'sm' | 'md' | 'lg' | 'xl' | 'full';
  border?: boolean;
  onClick?: () => void;
}

const Card = React.forwardRef<HTMLDivElement, CardProps>(
  ({
    children,
    className = '',
    hover = true,
    gradient = false,
    glass = false,
    padding = 'md',
    shadow = 'md',
    rounded = 'lg',
    border = false,
    onClick,
    ...props
  }, ref) => {
    // 修改 - 卡片基础过渡更克制 [Apple HIG]
const baseClasses = 'transition-colors duration-200';
    
    const paddingClasses = {
      none: '',
      sm: 'p-3',
      md: 'p-6',
      lg: 'p-8',
      xl: 'p-10',
    };

    const shadowClasses = {
      none: '',
      sm: 'shadow-sm',
      // 修改 - 卡片阴影更克制
      md: 'shadow-sm',
      lg: 'shadow',
      xl: 'shadow-md',
    };

    const roundedClasses = {
      none: '',
      sm: 'rounded-sm',
      md: 'rounded-md',
      lg: 'rounded-lg',
      xl: 'rounded-xl',
      full: 'rounded-full',
    };

    const backgroundClasses = glass
      ? 'surface-translucent'
      : gradient
        ? 'bg-white'
        : 'bg-white';

    const hoverClasses = hover && onClick
      ? 'cursor-pointer hover:bg-neutral-50'
      : hover
        ? 'hover:bg-neutral-50'
        : '';

    // 修改 - 使用发丝分隔线色值
const borderClasses = border ? 'border border-neutral-200/70' : '';

    const classes = `
      ${baseClasses}
      ${backgroundClasses}
      ${paddingClasses[padding]}
      ${shadowClasses[shadow]}
      ${roundedClasses[rounded]}
      ${hoverClasses}
      ${borderClasses}
      ${className}
    `.trim();

    const MotionDiv = motion.div;

    return (
      <MotionDiv
        ref={ref}
        className={classes}
        onClick={onClick}
        initial={{ opacity: 0, y: 20 }}
        whileInView={{ opacity: 1, y: 0 }}
        viewport={{ once: true }}
        transition={{ duration: 0.5 }}
        {...props}
      >
        {children}
      </MotionDiv>
    );
  }
);

Card.displayName = 'Card';

// 卡片头部组件
interface CardHeaderProps {
  children: React.ReactNode;
  className?: string;
}

export const CardHeader = React.forwardRef<HTMLDivElement, CardHeaderProps>(
  ({ children, className = '', ...props }, ref) => (
    <div
      ref={ref}
      className={`mb-4 ${className}`}
      {...props}
    >
      {children}
    </div>
  )
);

CardHeader.displayName = 'CardHeader';

// 卡片标题组件
interface CardTitleProps {
  children: React.ReactNode;
  className?: string;
  size?: 'sm' | 'md' | 'lg' | 'xl';
  gradient?: boolean;
}

export const CardTitle = React.forwardRef<HTMLHeadingElement, CardTitleProps>(
  ({ children, className = '', size = 'md', gradient = false, ...props }, ref) => {
    const sizeClasses = {
      sm: 'text-lg',
      md: 'text-xl',
      lg: 'text-2xl',
      xl: 'text-3xl',
    };

    const gradientClass = gradient ? 'gradient-text' : 'text-neutral-900';

    return (
      <h3
        ref={ref}
        className={`font-semibold ${sizeClasses[size]} ${gradientClass} ${className}`}
        {...props}
      >
        {children}
      </h3>
    );
  }
);

CardTitle.displayName = 'CardTitle';

// 卡片描述组件
interface CardDescriptionProps {
  children: React.ReactNode;
  className?: string;
}

export const CardDescription = React.forwardRef<HTMLParagraphElement, CardDescriptionProps>(
  ({ children, className = '', ...props }, ref) => (
    <p
      ref={ref}
      className={`text-neutral-600 ${className}`}
      {...props}
    >
      {children}
    </p>
  )
);

CardDescription.displayName = 'CardDescription';

// 卡片内容组件
interface CardContentProps {
  children: React.ReactNode;
  className?: string;
}

export const CardContent = React.forwardRef<HTMLDivElement, CardContentProps>(
  ({ children, className = '', ...props }, ref) => (
    <div
      ref={ref}
      className={className}
      {...props}
    >
      {children}
    </div>
  )
);

CardContent.displayName = 'CardContent';

// 卡片底部组件
interface CardFooterProps {
  children: React.ReactNode;
  className?: string;
}

export const CardFooter = React.forwardRef<HTMLDivElement, CardFooterProps>(
  ({ children, className = '', ...props }, ref) => (
    <div
      ref={ref}
      className={`mt-4 pt-4 border-t border-neutral-200/70 ${className}`}
      {...props}
    >
      {children}
    </div>
  )
);

CardFooter.displayName = 'CardFooter';

export default Card;
