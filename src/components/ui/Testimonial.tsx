'use client';
import React from 'react';
import Card, { CardContent } from './Card';

// 客户评价卡片
// Confirmed via mcp-feedback-enhanced
export interface TestimonialProps {
  quote: string;
  author: string;
  role?: string;
}

export default function Testimonial({ quote, author, role }: TestimonialProps) {
  return (
    <Card padding="lg" rounded="xl" hover className="bg-gradient-to-br from-white to-neutral-50">
      <CardContent>
        <p className="text-neutral-700 leading-relaxed">“{quote}”</p>
        <div className="mt-4 text-sm text-neutral-500">{author}{role ? ` · ${role}` : ''}</div>
      </CardContent>
    </Card>
  );
}

