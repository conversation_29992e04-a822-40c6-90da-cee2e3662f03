import React from 'react';

// 区块标题：主标题 + 副标题 + 轻分割线
// Confirmed via mcp-feedback-enhanced
interface SectionHeaderProps {
  title: string;
  subtitle?: string;
  align?: 'left' | 'center';
  className?: string;
}

export default function SectionHeader({ title, subtitle, align = 'center', className = '' }: SectionHeaderProps) {
  return (
    <div className={`mb-10 ${align === 'center' ? 'text-center' : ''} ${className}`.trim()}>
      {/* 修改 - 标题采用中性文本样式，移除渐变，贴近 Apple HIG */}
      {/* Confirmed via mcp-feedback-enhanced */}
      <h2 className="text-2xl md:text-3xl font-semibold tracking-[-0.01em] text-neutral-900">
        {title}
      </h2>
      {subtitle && (
        <p className="mt-3 text-neutral-600 max-w-2xl mx-auto">{subtitle}</p>
      )}
      {/* 修改 - 分割线改为发丝级中性线，移除彩色渐变 */}
      <div className="mt-6 h-px w-16 mx-auto bg-neutral-200/70" />
    </div>
  );
}

