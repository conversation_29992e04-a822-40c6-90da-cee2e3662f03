'use client';
import React from 'react';

// 简易跑马灯品牌墙（双列无缝滚动）
// Confirmed via mcp-feedback-enhanced
export default function Marquee({ items, speed = 25 }: { items: React.ReactNode[]; speed?: number }) {
  // speed 通过自定义 style 控制动画时长（秒）
  const duration = `${speed}s`;
  return (
    <div className="relative overflow-hidden">
      <div className="flex whitespace-nowrap" style={{ ['--tw-animate-duration' as unknown as string]: duration }}>
        <div className="flex animate-marquee gap-10 pr-10">
          {items.map((node, i) => (
            <div key={`a-${i}`} className="flex items-center opacity-70 hover:opacity-100 transition-opacity">{node}</div>
          ))}
        </div>
        <div className="flex animate-marquee gap-10 pr-10" aria-hidden>
          {items.map((node, i) => (
            <div key={`b-${i}`} className="flex items-center opacity-70 hover:opacity-100 transition-opacity">{node}</div>
          ))}
        </div>
      </div>
    </div>
  );
}

