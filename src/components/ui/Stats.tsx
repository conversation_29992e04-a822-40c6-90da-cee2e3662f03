import React from 'react';

// 统计组件，展示关键数字
// Confirmed via mcp-feedback-enhanced
export interface StatItem { label: string; value: string; }
export default function Stats({ items, className = '' }: { items: StatItem[]; className?: string }) {
  return (
    <div className={`grid grid-cols-2 sm:grid-cols-4 gap-4 ${className}`.trim()}>
      {items.map((it) => (
        <div key={it.label} className="p-6 bg-white rounded-2xl shadow-soft text-center">
          <div className="text-2xl font-bold text-neutral-900">{it.value}</div>
          <div className="text-neutral-500 text-sm mt-1">{it.label}</div>
        </div>
      ))}
    </div>
  );
}

