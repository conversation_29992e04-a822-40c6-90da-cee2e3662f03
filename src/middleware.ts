import { NextRequest, NextResponse } from 'next/server';
import { locales, detectUserLanguage } from './lib/i18n';

export function middleware(request: NextRequest) {
  const pathname = request.nextUrl.pathname;
  
  // 检查路径是否已经包含语言代码
  const pathnameHasLocale = locales.some(
    (locale) => pathname.startsWith(`/${locale}/`) || pathname === `/${locale}`
  );

  if (pathnameHasLocale) {
    return NextResponse.next();
  }

  // 检测用户首选语言
  const acceptLanguage = request.headers.get('accept-language') || '';
  const detectedLocale = detectUserLanguage(acceptLanguage);

  // 重定向到检测到的语言版本
  const newUrl = new URL(`/${detectedLocale}${pathname}`, request.url);
  return NextResponse.redirect(newUrl);
}

export const config = {
  matcher: [
    // 匹配所有路径，除了静态资源和API路由
    '/((?!api|_next/static|_next/image|favicon.ico|robots.txt|sitemap.xml).*)',
  ],
}; 