import zhMessages from '../../messages/zh.json';
import enMessages from '../../messages/en.json';
import frMessages from '../../messages/fr.json';
import deMessages from '../../messages/de.json';
import jaMessages from '../../messages/ja.json';
import koMessages from '../../messages/ko.json';
import esMessages from '../../messages/es.json';
import ptMessages from '../../messages/pt.json';
import ruMessages from '../../messages/ru.json';
import itMessages from '../../messages/it.json';

export const locales = ['zh', 'en', 'fr', 'de', 'ja', 'ko', 'es', 'pt', 'ru', 'it'] as const;
export type Locale = typeof locales[number];

const messages = {
  zh: zhMessages,
  en: enMessages,
  fr: frMessages,
  de: deMessages,
  ja: jaMessages,
  ko: koMessages,
  es: esMessages,
  pt: ptMessages,
  ru: ruMessages,
  it: itMessages,
};

export function getMessages(locale: Locale) {
  return messages[locale] || messages.zh;
}

export function t(path: string, locale: Locale): string {
  const keys = path.split('.');
  let value: Record<string, unknown> = getMessages(locale);
  
  for (const key of keys) {
    if (value && typeof value === 'object' && key in value) {
      value = value[key] as Record<string, unknown>;
    } else {
      return path;
    }
  }
  
  return (typeof value === 'string' ? value : path);
}

// 语言映射配置，用于自动检测
export const languageMapping: Record<string, Locale> = {
  'zh': 'zh',
  'zh-CN': 'zh',
  'zh-TW': 'zh',
  'zh-HK': 'zh',
  'en': 'en',
  'en-US': 'en',
  'en-GB': 'en',
  'fr': 'fr',
  'fr-FR': 'fr',
  'fr-CA': 'fr',
  'de': 'de',
  'de-DE': 'de',
  'de-AT': 'de',
  'de-CH': 'de',
  'ja': 'ja',
  'ja-JP': 'ja',
  'ko': 'ko',
  'ko-KR': 'ko',
  'es': 'es',
  'es-ES': 'es',
  'es-MX': 'es',
  'es-AR': 'es',
  'es-CO': 'es',
  'pt': 'pt',
  'pt-BR': 'pt',
  'pt-PT': 'pt',
  'ru': 'ru',
  'ru-RU': 'ru',
  'it': 'it',
  'it-IT': 'it',
};

// 检测用户首选语言
export function detectUserLanguage(acceptLanguage?: string): Locale {
  if (!acceptLanguage) return 'en';
  
  // 解析 Accept-Language 头部
  const languages = acceptLanguage
    .split(',')
    .map(lang => {
      const [language, quality = '1'] = lang.trim().split(';q=');
      return { language: language.trim(), quality: parseFloat(quality) };
    })
    .sort((a, b) => b.quality - a.quality);

  // 寻找匹配的语言
  for (const { language } of languages) {
    const locale = languageMapping[language] || languageMapping[language.split('-')[0]];
    if (locale && locales.includes(locale)) {
      return locale;
    }
  }

  return 'en'; 
} 