import type { Metadata } from "next";
import { notFound } from 'next/navigation';
import "../globals.css";
import Header from "@/components/Header";
import Footer from "@/components/Footer";
import { locales, type Locale } from "@/lib/i18n";

export async function generateMetadata({ params }: { params: Promise<{ locale: string }> }): Promise<Metadata> {
  const { locale } = await params;
  const isZh = locale === 'zh';
  
  return {
    title: isZh ? "三娃软件开发工作室 - 专业的软件开发服务" : "Sanva Software Development Studio - Professional Software Development Services",
    description: isZh 
      ? "专业的软件开发服务，包括应用开发、小程序开发、海外平台开发和后端开发"
      : "Professional software development services including app development, mini-program development, global platform development, and backend development",
    keywords: isZh 
      ? "软件开发,应用开发,小程序开发,海外平台开发,网站建设,后端开发,全栈开发,数字化转型,企业应用,移动应用,微信小程序,Google Play,App Store,Facebook,WhatsApp,React开发,Node.js开发,三娃软件,技术服务,软件工作室"
      : "software development,app development,mini program development,global platform development,web development,backend development,full stack development,digital transformation,enterprise applications,mobile applications,Google Play,App Store,Facebook,WhatsApp,React development,Node.js development,Sanva Software,technical services,software studio",
    authors: [{ name: isZh ? "三娃软件开发工作室" : "Sanva Software Development Studio" }],
    creator: isZh ? "三娃软件开发工作室" : "Sanva Software Development Studio",
    publisher: isZh ? "三娃软件开发工作室" : "Sanva Software Development Studio",
    robots: "index, follow",
    openGraph: {
      title: isZh ? "三娃软件开发工作室 - 专业的软件开发服务" : "Sanva Software Development Studio - Professional Software Development Services",
      description: isZh 
        ? "专注于高质量软件解决方案，涵盖移动应用、小程序、海外平台、网站建设等全栈技术服务"
        : "Focus on high-quality software solutions, covering mobile applications, mini-programs, global platforms, website development and other full-stack technical services",
      url: "https://sanva.top",
      siteName: isZh ? "三娃软件开发工作室" : "Sanva Software Development Studio",
      locale: locale === 'zh' ? 'zh_CN' : locale,
      type: "website",
    },
    twitter: {
      card: "summary_large_image",
      title: isZh ? "三娃软件开发工作室 - 专业的软件开发服务" : "Sanva Software Development Studio - Professional Software Development Services",
      description: isZh 
        ? "专注于高质量软件解决方案，涵盖移动应用、小程序、海外平台、网站建设等全栈技术服务"
        : "Focus on high-quality software solutions, covering mobile applications, mini-programs, global platforms, website development and other full-stack technical services",
    },
    alternates: {
      canonical: `https://sanva.top/${locale}`,
      languages: {
        'zh': 'https://sanva.top/zh',
        'en': 'https://sanva.top/en',
        'fr': 'https://sanva.top/fr',
        'de': 'https://sanva.top/de',
        'ja': 'https://sanva.top/ja',
        'ko': 'https://sanva.top/ko',
      },
    },
    category: "technology",
  };
}

export default async function LocaleLayout({
  children,
  params
}: {
  children: React.ReactNode;
  params: Promise<{ locale: string }>;
}) {
  const { locale } = await params;
  // Validate that the incoming `locale` parameter is valid
  if (!locales.includes(locale as Locale)) notFound();

  return (
    <div className="min-h-screen flex flex-col">
      {/* 无障碍辅助链接：聚焦时显示，便于键盘用户跳转到主内容 */}
      {/* Confirmed via mcp-feedback-enhanced */}
      <a href="#main-content" className="skip-link">跳到主内容</a>
      <Header locale={locale as Locale} />
      <main id="main-content" className="flex-grow">{children}</main>
      <Footer locale={locale as Locale} />
    </div>
  );
} 