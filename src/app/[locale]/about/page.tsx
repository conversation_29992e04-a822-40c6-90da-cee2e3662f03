'use client';

import { motion } from "framer-motion";
import { useParams } from "next/navigation";
import { t, type Locale } from '@/lib/i18n';
import Hero from '@/components/ui/Hero';
import Card, { <PERSON><PERSON>onte<PERSON>, CardHeader, CardTitle } from '@/components/ui/Card';
import Button from '@/components/ui/Button';
import Badge from '@/components/ui/Badge';
import { FeatureIcons, NavigationIcons } from '@/components/ui/Icons';

// 动画配置
const fadeInUp = {
  hidden: { opacity: 0, y: 20 },
  visible: { opacity: 1, y: 0 },
};

const staggerContainer = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
    },
  },
};

// 现代化价值观卡片组件
const ModernValueCard = ({ title, description, icon, locale }: {
  title: string;
  description: string;
  icon: string;
  locale: Locale;
}) => (
  <motion.div
    variants={fadeInUp}
    className="group"
  >
    <Card
      className="h-full transition-all duration-300 hover:scale-105 hover:shadow-xl border border-neutral-200 hover:border-primary-200 text-center"
      padding="lg"
      shadow="lg"
      border={false}
    >
      <CardContent className="p-8">
        <div className="text-5xl mb-6 group-hover:scale-110 transition-transform duration-300">
          {icon}
        </div>
        <h3 className="text-xl font-semibold text-neutral-900 mb-4 group-hover:text-primary-600 transition-colors">
          {title}
        </h3>
        <p className="text-neutral-600 leading-relaxed">
          {description}
        </p>
      </CardContent>
    </Card>
  </motion.div>
);

// 统计数据卡片组件
const StatCard = ({ number, label, icon }: { number: string; label: string; icon: string }) => (
  <motion.div
    variants={fadeInUp}
    className="text-center"
  >
    <div className="text-3xl mb-3">{icon}</div>
    <div className="text-4xl font-bold text-primary-600 mb-2">{number}</div>
    <div className="text-neutral-600">{label}</div>
  </motion.div>
);

// 里程碑卡片组件
const MilestoneCard = ({ year, title, description }: { year: string; title: string; description: string }) => (
  <motion.div
    variants={fadeInUp}
    className="flex items-start space-x-4"
  >
    <div className="flex-shrink-0">
      <div className="w-12 h-12 bg-primary-100 rounded-full flex items-center justify-center">
        <div className="w-6 h-6 bg-primary-600 rounded-full"></div>
      </div>
    </div>
    <div>
      <div className="text-sm font-medium text-primary-600 mb-1">{year}</div>
      <h3 className="text-lg font-semibold text-neutral-900 mb-2">{title}</h3>
      <p className="text-neutral-600">{description}</p>
    </div>
  </motion.div>
);

export default function About() {
  const params = useParams();
  const locale = params.locale as string;
  const localeTyped = locale as Locale;
  const isZh = localeTyped === 'zh';

  // 统计数据
  const stats = [
    { number: '50+', label: isZh ? '成功项目' : 'Successful Projects', icon: '🚀' },
    { number: '98%', label: isZh ? '客户满意度' : 'Client Satisfaction', icon: '⭐' },
    { number: '2年+', label: isZh ? '行业经验' : 'Industry Experience', icon: '💼' },
    { number: '24/7', label: isZh ? '技术支持' : 'Technical Support', icon: '🛠️' }
  ];

  // 核心价值观数据
  const values = [
    {
      title: isZh ? '专业卓越' : 'Professional Excellence',
      description: isZh
        ? '我们拥有丰富的技术经验和行业知识，始终追求技术卓越，为客户提供最专业的软件解决方案。'
        : 'We have rich technical experience and industry knowledge, always pursuing technical excellence to provide the most professional software solutions for our clients.',
      icon: "🏆"
    },
    {
      title: isZh ? '高效交付' : 'Efficient Delivery',
      description: isZh
        ? '我们注重开发效率和项目进度管理，采用敏捷开发方法，确保按时交付高质量的产品。'
        : 'We focus on development efficiency and project management, using agile development methods to ensure timely delivery of high-quality products.',
      icon: "⚡"
    },
    {
      title: isZh ? '客户至上' : 'Customer First',
      description: isZh
        ? '我们以客户需求为中心，提供个性化解决方案和贴心的售后服务，确保客户获得最佳体验。'
        : 'We center on customer needs, providing personalized solutions and thoughtful after-sales service to ensure customers get the best experience.',
      icon: "🤝"
    },
    {
      title: isZh ? '持续创新' : 'Continuous Innovation',
      description: isZh
        ? '我们紧跟技术发展趋势，不断学习和应用新技术，为客户提供前沿的技术解决方案。'
        : 'We keep up with technology trends, continuously learn and apply new technologies to provide cutting-edge technical solutions for clients.',
      icon: "💡"
    }
  ];

  // 公司里程碑
  const milestones = [
    {
      year: '2023',
      title: isZh ? '工作室成立' : 'Studio Founded',
      description: isZh
        ? '三娃软件开发工作室正式成立，专注于为企业和个人提供高质量软件解决方案。'
        : 'Sanva Software Development Studio was officially founded, focusing on providing high-quality software solutions for enterprises and individuals.'
    },
    {
      year: '2023',
      title: isZh ? '首个项目成功交付' : 'First Project Delivered',
      description: isZh
        ? '成功交付首个企业级项目，获得客户高度认可，奠定了工作室的技术实力基础。'
        : 'Successfully delivered the first enterprise-level project, received high recognition from clients, laying the foundation for the studio\'s technical strength.'
    },
    {
      year: '2024',
      title: isZh ? '团队扩展' : 'Team Expansion',
      description: isZh
        ? '团队规模扩展至12人，涵盖前端、后端、设计、产品等多个专业领域。'
        : 'Team expanded to 12 people, covering multiple professional fields including frontend, backend, design, and product.'
    },
    {
      year: '2024',
      title: isZh ? '技术突破' : 'Technical Breakthrough',
      description: isZh
        ? '在微服务架构和云原生技术方面取得重大突破，为客户提供更加稳定高效的解决方案。'
        : 'Made major breakthroughs in microservices architecture and cloud-native technologies, providing more stable and efficient solutions for clients.'
    },
    {
      year: '2025',
      title: isZh ? '持续发展' : 'Continuous Growth',
      description: isZh
        ? '继续深耕技术领域，拓展服务范围，致力于成为客户最信赖的技术合作伙伴。'
        : 'Continue to deepen in the technical field, expand service scope, and strive to become the most trusted technical partner for clients.'
    }
  ];

  return (
    <>
      {/* 页面标题 - 统一 Apple 风格 Hero：白底 + 中性文案 */}
      {/* Confirmed via mcp-feedback-enhanced */}
      <Hero
        title={t('about.title', localeTyped)}
        subtitle={t('about.subtitle', localeTyped)}
        align="left"
      />

      {/* 工作室简介 */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="md:flex md:items-center md:space-x-12">
            <motion.div 
              className="md:w-1/2 mb-8 md:mb-0"
              initial={{ opacity: 0, x: -50 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.5 }}
            >
              <h2 className="text-2xl md:text-3xl font-semibold tracking-[-0.01em] text-neutral-900 mb-6">
                {t('about.introduction.title', localeTyped)}
              </h2>
              <div className="text-neutral-600 space-y-4">
                <p>
                  {t('about.introduction.paragraphs.first', localeTyped)}
                </p>
                <p>
                  {t('about.introduction.paragraphs.second', localeTyped)}
                </p>
                <p>
                  {t('about.introduction.paragraphs.third', localeTyped)}
                </p>
              </div>
            </motion.div>
            <motion.div 
              className="md:w-1/2"
              initial={{ opacity: 0, x: 50 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.5, delay: 0.2 }}
            >
              {/* 使用抽象插画占位，后续可替换为真实团队/办公环境图片 - Confirmed via mcp-feedback-enhanced */}
              <div className="bg-neutral-100 rounded-lg h-80 flex items-center justify-center">
                <div className="w-40 h-40 rounded-2xl bg-white/70 shadow-colored flex items-center justify-center">
                  <span className="text-5xl">🏢</span>
                </div>
              </div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* 核心价值观 */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div 
            className="text-center mb-12"
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
            variants={fadeInUp}
          >
            <h2 className="text-2xl md:text-3xl font-semibold tracking-[-0.01em] text-neutral-900 mb-4">
              {t('about.values.title', localeTyped)}
            </h2>
            <p className="text-lg text-neutral-600 max-w-3xl mx-auto">
              {t('about.values.subtitle', localeTyped)}
            </p>
          </motion.div>
          <div className="grid md:grid-cols-3 gap-8">
            {values.map((value, index) => (
              <ModernValueCard
                key={`${index}-${locale}`}
                title={value.title}
                description={value.description}
                icon={value.icon}
                locale={localeTyped}
              />
            ))}
          </div>
        </div>
      </section>

      {/* 统计数据部分 */}
      <section className="py-16 bg-neutral-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
            variants={staggerContainer}
            className="grid md:grid-cols-4 gap-8"
          >
            {stats.map((stat, index) => (
              <StatCard key={index} {...stat} />
            ))}
          </motion.div>
        </div>
      </section>

      {/* 核心价值观部分 - 重新设计 */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
            variants={fadeInUp}
            className="text-center mb-16"
          >
            <h2 className="text-4xl font-semibold text-neutral-900 mb-6">
              {isZh ? '我们的价值观' : 'Our Values'}
            </h2>
            <p className="text-xl text-neutral-600 max-w-3xl mx-auto">
              {isZh
                ? '这些核心价值观指导着我们的工作方式，塑造着我们的团队文化，确保我们为客户提供最优质的服务'
                : 'These core values guide our way of working, shape our team culture, and ensure we provide the highest quality service to our clients'}
            </p>
          </motion.div>

          <motion.div
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
            variants={staggerContainer}
            className="grid md:grid-cols-2 lg:grid-cols-4 gap-8"
          >
            {values.map((value, index) => (
              <ModernValueCard key={index} {...value} locale={localeTyped} />
            ))}
          </motion.div>
        </div>
      </section>

      {/* 公司历程部分 */}
      <section className="py-20 bg-neutral-50">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
            variants={fadeInUp}
            className="text-center mb-16"
          >
            <h2 className="text-4xl font-semibold text-neutral-900 mb-6">
              {isZh ? '发展历程' : 'Our Journey'}
            </h2>
            <p className="text-xl text-neutral-600">
              {isZh
                ? '从初创到成长，我们一步步走向专业化和规模化'
                : 'From startup to growth, we are moving step by step towards professionalization and scale'}
            </p>
          </motion.div>

          <motion.div
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
            variants={staggerContainer}
            className="space-y-8"
          >
            {milestones.map((milestone, index) => (
              <MilestoneCard key={index} {...milestone} />
            ))}
          </motion.div>
        </div>
      </section>

      {/* 技术实力部分 */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
            variants={fadeInUp}
            className="text-center mb-16"
          >
            <h2 className="text-4xl font-semibold text-neutral-900 mb-6">
              {isZh ? '技术实力' : 'Technical Expertise'}
            </h2>
            <p className="text-xl text-neutral-600 max-w-3xl mx-auto mb-12">
              {isZh
                ? '我们掌握最新的技术栈，能够为客户提供全栈解决方案'
                : 'We master the latest technology stack and can provide full-stack solutions for clients'}
            </p>
          </motion.div>

          <motion.div
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
            variants={staggerContainer}
            className="grid md:grid-cols-3 gap-8"
          >
            {[
              {
                title: isZh ? '前端开发' : 'Frontend Development',
                description: isZh
                  ? '精通 React、Vue.js、Next.js 等现代前端框架，能够构建高性能、响应式的用户界面。'
                  : 'Proficient in modern frontend frameworks like React, Vue.js, Next.js, capable of building high-performance, responsive user interfaces.',
                icon: '🎨',
                technologies: ['React', 'Vue.js', 'Next.js', 'TypeScript', 'Tailwind CSS']
              },
              {
                title: isZh ? '后端开发' : 'Backend Development',
                description: isZh
                  ? '擅长 Node.js、Python、Java 等后端技术，具备微服务架构和云原生开发经验。'
                  : 'Skilled in backend technologies like Node.js, Python, Java, with experience in microservices architecture and cloud-native development.',
                icon: '⚙️',
                technologies: ['Node.js', 'Python', 'Java', 'PostgreSQL', 'Redis']
              },
              {
                title: isZh ? '云原生' : 'Cloud Native',
                description: isZh
                  ? '熟练使用 Docker、Kubernetes、AWS 等云原生技术，提供可扩展的部署解决方案。'
                  : 'Proficient in cloud-native technologies like Docker, Kubernetes, AWS, providing scalable deployment solutions.',
                icon: '☁️',
                technologies: ['Docker', 'Kubernetes', 'AWS', 'CI/CD', 'Terraform']
              }
            ].map((tech, index) => (
              <motion.div
                key={index}
                variants={fadeInUp}
                className="group"
              >
                <Card
                  className="h-full transition-all duration-300 hover:scale-105 hover:shadow-xl border border-neutral-200 hover:border-primary-200"
                  padding="lg"
                  shadow="lg"
                  border={false}
                >
                  <CardContent className="p-8">
                    <div className="text-4xl mb-6 group-hover:scale-110 transition-transform duration-300">
                      {tech.icon}
                    </div>
                    <h3 className="text-xl font-semibold text-neutral-900 mb-4 group-hover:text-primary-600 transition-colors">
                      {tech.title}
                    </h3>
                    <p className="text-neutral-600 leading-relaxed mb-6">
                      {tech.description}
                    </p>
                    <div className="flex flex-wrap gap-2">
                      {tech.technologies.map((technology, techIndex) => (
                        <Badge key={techIndex} variant="outline" size="sm">
                          {technology}
                        </Badge>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </motion.div>
        </div>
      </section>

      {/* CTA 部分 */}
      <section className="py-20 bg-primary-600">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <motion.div
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
            variants={fadeInUp}
          >
            <h2 className="text-4xl font-semibold text-white mb-6">
              {isZh ? '准备开始您的项目了吗？' : 'Ready to Start Your Project?'}
            </h2>
            <p className="text-xl text-primary-100 mb-8">
              {isZh
                ? '让我们一起讨论您的需求，为您提供最适合的技术解决方案'
                : 'Let\'s discuss your requirements together and provide you with the most suitable technical solution'}
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button
                variant="secondary"
                size="lg"
                onClick={() => window.location.href = `/${locale}/contact`}
                icon={NavigationIcons.Arrow}
                iconPosition="right"
              >
                {isZh ? '联系我们' : 'Contact Us'}
              </Button>
              <Button
                variant="outline"
                size="lg"
                onClick={() => window.location.href = `/${locale}/portfolio`}
                className="border-white text-white hover:bg-white hover:text-primary-600"
              >
                {isZh ? '查看案例' : 'View Portfolio'}
              </Button>
            </div>
          </motion.div>
        </div>
      </section>
    </>
  );
}