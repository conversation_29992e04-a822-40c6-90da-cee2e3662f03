'use client';

import { motion } from "framer-motion";
import { useParams } from "next/navigation";
import { t, type Locale } from '@/lib/i18n';
import Card, { CardHeader, CardTitle, CardDescription, CardContent, CardFooter } from '@/components/ui/Card';
import Button from '@/components/ui/Button';
import Badge from '@/components/ui/Badge';
// 新增 - 统一 Hero 组件（Apple 风格）
import Hero from '@/components/ui/Hero';
import {
  FeatureIcons,
  ToolIcons,
  NavigationIcons
} from '@/components/ui/Icons';

// 动画配置
const fadeInUp = {
  hidden: { opacity: 0, y: 20 },
  visible: { opacity: 1, y: 0 },
};

// 项目卡片组件
const ProjectCard = ({
  title,
  description,
  technologies,
  category,
  image,
  demoUrl,
  features,
  locale
}: {
  title: string;
  description: string;
  technologies: string[];
  category: string;
  image: string;
  demoUrl?: string;
  features: string[];
  locale: Locale;
}) => (
  <Card hover className="overflow-hidden group">
    {/* Apple 风格简洁设计 - Confirmed via mcp-feedback-enhanced */}
    <div className="relative h-48 bg-neutral-50 flex items-center justify-center overflow-hidden">
      <div className="absolute inset-0 bg-grid opacity-20" />
      <div className="absolute inset-0 bg-noise opacity-30" />
      <div className="relative w-24 h-24 rounded-2xl bg-gradient-to-br from-white/70 to-white/40 shadow-colored flex items-center justify-center hover-zoom">
        <FeatureIcons.Code className="w-10 h-10 text-primary-600" />
      </div>
      <div className="absolute inset-0 bg-black/0 group-hover:bg-black/10 transition-all duration-300"></div>
    </div>
    <CardContent className="p-6">
      <div className="flex items-center justify-between mb-3">
        <span className="px-3 py-1 bg-primary-100 text-primary-700 text-xs font-medium rounded-full">
          {category}
        </span>
        {demoUrl && (
          <Button size="sm" variant="ghost" icon={NavigationIcons.Arrow} iconPosition="right">
            {t('portfolio.viewDetails', locale)}
          </Button>
        )}
      </div>
      <CardTitle size="md" className="mb-3 group-hover:text-primary-600 transition-colors">
        {title}
      </CardTitle>
      <CardDescription className="mb-4 leading-relaxed">
        {description}
      </CardDescription>
      <div className="mb-4">
        <h4 className="text-sm font-medium text-neutral-700 mb-2">{t('portfolio.coreFeatures', locale)}</h4>
        <ul className="space-y-1">
          {features.map((feature, index) => (
            <li key={index} className="flex items-center text-sm text-neutral-600">
              <FeatureIcons.Star className="w-3 h-3 text-secondary-500 mr-2 flex-shrink-0" />
              {feature}
            </li>
          ))}
        </ul>
      </div>
      <div className="flex flex-wrap gap-2">
        {technologies.map((tech, index) => (
          <Badge key={index} color="neutral" soft>
            {tech}
          </Badge>
        ))}
      </div>
    </CardContent>
  </Card>
);

export default function Portfolio() {
  const params = useParams();
  const locale = params.locale as string;
  const localeTyped = locale as Locale;

  // 项目数据
  const projects = localeTyped === 'zh' ? [
    {
      title: "智能电商小程序",
      description: "为某知名品牌开发的微信小程序，集成了商品展示、在线支付、用户管理等功能。",
      category: "小程序开发",
      image: "🛒",
      technologies: ["微信小程序", "Node.js", "MongoDB", "微信支付"],
      features: [
        "商品浏览和搜索",
        "购物车和订单管理",
        "微信支付集成",
        "用户积分系统",
        "客服聊天功能"
      ],
      demoUrl: "#"
    },
    {
      title: "企业管理系统",
      description: "为中型企业定制开发的综合管理系统，包含人事、财务、项目管理等模块。",
      category: "Web应用",
      image: "📊",
      technologies: ["React", "TypeScript", "Node.js", "PostgreSQL"],
      features: [
        "员工信息管理",
        "财务报表生成",
        "项目进度跟踪",
        "权限管理系统",
        "数据可视化"
      ],
      demoUrl: "#"
    },
    {
      title: "移动健康应用",
      description: "跨平台健康管理应用，支持健康数据记录、运动计划制定和社区交流。",
      category: "移动应用",
      image: "💪",
      technologies: ["React Native", "Firebase", "Redux", "Chart.js"],
      features: [
        "健康数据记录",
        "运动计划定制",
        "社区互动功能",
        "数据同步云端",
        "智能提醒系统"
      ],
      demoUrl: "#"
    },
    {
      title: "在线教育平台",
      description: "面向K12教育的在线学习平台，支持直播授课、作业管理和学习进度跟踪。",
      category: "教育科技",
      image: "📚",
      technologies: ["Vue.js", "WebRTC", "Socket.io", "MySQL"],
      features: [
        "实时视频直播",
        "互动白板功能",
        "作业批改系统",
        "学习进度分析",
        "家长监督功能"
      ],
      demoUrl: "#"
    },
    {
      title: "智能物联网系统",
      description: "为智能家居开发的物联网控制系统，支持多设备管理和自动化场景。",
      category: "物联网",
      image: "🏠",
      technologies: ["Python", "MQTT", "Redis", "Docker"],
      features: [
        "设备远程控制",
        "场景自动化",
        "数据监控分析",
        "语音控制集成",
        "安全防护系统"
      ],
      demoUrl: "#"
    },
    {
      title: "区块链钱包应用",
      description: "安全可靠的数字货币钱包应用，支持多币种管理和DeFi功能。",
      category: "区块链",
      image: "💰",
      technologies: ["Solidity", "Web3.js", "React", "MetaMask"],
      features: [
        "多币种支持",
        "DeFi协议集成",
        "安全私钥管理",
        "交易记录查询",
        "市场行情显示"
      ],
      demoUrl: "#"
    }
  ] : [
    {
      title: "Smart E-commerce Mini Program",
      description: "WeChat mini-program for a renowned brand, featuring product display, online payment, and user management.",
      category: "Mini Program",
      image: "🛒",
      technologies: ["WeChat Mini Program", "Node.js", "MongoDB", "WeChat Pay"],
      features: [
        "Product browsing and search",
        "Shopping cart and order management",
        "WeChat Pay integration",
        "User points system",
        "Customer service chat"
      ],
      demoUrl: "#"
    },
    {
      title: "Enterprise Management System",
      description: "Comprehensive management system for medium-sized enterprises, including HR, finance, and project management modules.",
      category: "Web Application",
      image: "📊",
      technologies: ["React", "TypeScript", "Node.js", "PostgreSQL"],
      features: [
        "Employee information management",
        "Financial report generation",
        "Project progress tracking",
        "Permission management system",
        "Data visualization"
      ],
      demoUrl: "#"
    },
    {
      title: "Mobile Health App",
      description: "Cross-platform health management app supporting health data recording, exercise planning, and community interaction.",
      category: "Mobile App",
      image: "💪",
      technologies: ["React Native", "Firebase", "Redux", "Chart.js"],
      features: [
        "Health data recording",
        "Custom exercise plans",
        "Community interaction",
        "Cloud data sync",
        "Smart reminder system"
      ],
      demoUrl: "#"
    },
    {
      title: "Online Education Platform",
      description: "K12-focused online learning platform supporting live teaching, homework management, and progress tracking.",
      category: "EdTech",
      image: "📚",
      technologies: ["Vue.js", "WebRTC", "Socket.io", "MySQL"],
      features: [
        "Real-time video streaming",
        "Interactive whiteboard",
        "Homework grading system",
        "Learning progress analysis",
        "Parent supervision"
      ],
      demoUrl: "#"
    },
    {
      title: "Smart IoT System",
      description: "IoT control system for smart homes, supporting multi-device management and automation scenarios.",
      category: "IoT",
      image: "🏠",
      technologies: ["Python", "MQTT", "Redis", "Docker"],
      features: [
        "Remote device control",
        "Scene automation",
        "Data monitoring and analysis",
        "Voice control integration",
        "Security protection system"
      ],
      demoUrl: "#"
    },
    {
      title: "Blockchain Wallet App",
      description: "Secure and reliable digital currency wallet app supporting multi-currency management and DeFi features.",
      category: "Blockchain",
      image: "💰",
      technologies: ["Solidity", "Web3.js", "React", "MetaMask"],
      features: [
        "Multi-currency support",
        "DeFi protocol integration",
        "Secure private key management",
        "Transaction history query",
        "Market price display"
      ],
      demoUrl: "#"
    }
  ];

  return (
    <>
      {/* 页面标题 - 统一 Apple 风格 Hero：白底 + 中性文案 */}
      {/* Confirmed via mcp-feedback-enhanced */}
      <Hero
        title={localeTyped === 'zh' ? '项目作品集' : 'Portfolio'}
        subtitle={localeTyped === 'zh'
          ? '展示我们为客户打造的优秀数字解决方案，每个项目都体现了我们的专业技能和创新思维。'
          : "Showcasing excellent digital solutions we've created for our clients, each project reflects our professional skills and innovative thinking."}
        align="left"
      />

      {/* 项目展示 */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            className="text-center mb-16"
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
            variants={fadeInUp}
          >
            <h2 className="text-4xl md:text-5xl font-semibold tracking-[-0.015em] text-neutral-900 mb-6">
              {localeTyped === 'zh' ? '精选项目' : 'Featured Projects'}
            </h2>
            <p className="text-xl text-neutral-600 max-w-4xl mx-auto leading-relaxed">
              {localeTyped === 'zh'
                ? '从小程序到企业级应用，从移动端到Web端，我们的项目覆盖多个行业和技术领域。'
                : 'From mini-programs to enterprise applications, from mobile to web, our projects span multiple industries and technology domains.'
              }
            </p>
          </motion.div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {projects.map((project, index) => (
              <motion.div
                key={`${index}-${locale}`}
                initial="hidden"
                whileInView="visible"
                viewport={{ once: true }}
                variants={fadeInUp}
                transition={{ duration: 0.5, delay: index * 0.1 }}
              >
                <ProjectCard {...project} locale={localeTyped} />
              </motion.div>
            ))}
          </div>
        </div>
      </section>
    </>
  );
}
