'use client';
import React from 'react';
import { motion } from "framer-motion";
import { useParams } from 'next/navigation';
import { t, type Locale } from '@/lib/i18n';
import Hero from '@/components/ui/Hero';
import Card, { CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import Button from '@/components/ui/Button';
import Badge from '@/components/ui/Badge';
import { FeatureIcons, NavigationIcons, ContactIcons, SocialIcons } from '@/components/ui/Icons';

// 动画配置
const fadeInUp = {
  hidden: { opacity: 0, y: 20 },
  visible: { opacity: 1, y: 0 },
};

const staggerContainer = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
    },
  },
};

// 现代化团队成员卡片组件
const ModernMemberCard = ({
  name,
  role,
  avatar,
  bio,
  skills,
  experience,
  education,
  achievements,
  social,
  locale
}: {
  name: string;
  role: string;
  avatar: string;
  bio: string;
  skills: string[];
  experience: string;
  education: string;
  achievements: string[];
  social?: {
    github?: string;
    linkedin?: string;
    email?: string;
  };
  locale: Locale;
}) => (
  <motion.div
    variants={fadeInUp}
    className="group"
  >
    <Card
      className="h-full transition-all duration-300 hover:scale-105 hover:shadow-xl border border-neutral-200 hover:border-primary-200"
      padding="none"
      shadow="lg"
      border={false}
    >
      {/* 头像和基本信息 */}
      <CardHeader className="p-6 text-center">
        <div className="relative mx-auto mb-4">
          <div className="w-24 h-24 rounded-full bg-gradient-to-br from-primary-100 to-primary-200 flex items-center justify-center text-3xl font-bold text-primary-600 group-hover:scale-110 transition-transform duration-300">
            {avatar}
          </div>
          <div className="absolute -bottom-2 -right-2 w-8 h-8 bg-green-500 rounded-full border-4 border-white flex items-center justify-center">
            <div className="w-3 h-3 bg-white rounded-full"></div>
          </div>
        </div>
        <h3 className="text-xl font-semibold text-neutral-900 mb-1 group-hover:text-primary-600 transition-colors">
          {name}
        </h3>
        <p className="text-primary-600 font-medium mb-2">{role}</p>
        <p className="text-sm text-neutral-600 leading-relaxed">{bio}</p>
      </CardHeader>

      <CardContent className="p-6 pt-0">
        {/* 经验和教育背景 */}
        <div className="mb-6 space-y-3">
          <div className="flex items-start">
            <FeatureIcons.Star className="w-4 h-4 text-primary-600 mr-2 mt-0.5 flex-shrink-0" />
            <div>
              <div className="text-xs font-medium text-neutral-700 mb-1">
                {locale === 'zh' ? '工作经验' : 'Experience'}
              </div>
              <div className="text-sm text-neutral-600">{experience}</div>
            </div>
          </div>
          <div className="flex items-start">
            <FeatureIcons.Code className="w-4 h-4 text-primary-600 mr-2 mt-0.5 flex-shrink-0" />
            <div>
              <div className="text-xs font-medium text-neutral-700 mb-1">
                {locale === 'zh' ? '教育背景' : 'Education'}
              </div>
              <div className="text-sm text-neutral-600">{education}</div>
            </div>
          </div>
        </div>

        {/* 技能标签 */}
        <div className="mb-6">
          <h4 className="text-sm font-medium text-neutral-700 mb-3">
            {locale === 'zh' ? '核心技能' : 'Core Skills'}
          </h4>
          <div className="flex flex-wrap gap-2">
            {skills.map((skill, index) => (
              <Badge key={index} variant="outline" size="sm">
                {skill}
              </Badge>
            ))}
          </div>
        </div>

        {/* 主要成就 */}
        <div className="mb-6">
          <h4 className="text-sm font-medium text-neutral-700 mb-3">
            {locale === 'zh' ? '主要成就' : 'Key Achievements'}
          </h4>
          <ul className="space-y-2">
            {achievements.map((achievement, index) => (
              <li key={index} className="flex items-start text-sm text-neutral-600">
                <div className="w-1.5 h-1.5 rounded-full bg-primary-500 mr-2 mt-2 flex-shrink-0"></div>
                {achievement}
              </li>
            ))}
          </ul>
        </div>

        {/* 社交链接 */}
        {social && (
          <div className="flex justify-center space-x-3 pt-4 border-t border-neutral-100">
            {social.github && (
              <a
                href={social.github}
                target="_blank"
                rel="noopener noreferrer"
                className="p-2 text-neutral-400 hover:text-neutral-600 transition-colors"
              >
                <SocialIcons.Github className="w-5 h-5" />
              </a>
            )}
            {social.linkedin && (
              <a
                href={social.linkedin}
                target="_blank"
                rel="noopener noreferrer"
                className="p-2 text-neutral-400 hover:text-neutral-600 transition-colors"
              >
                <SocialIcons.Linkedin className="w-5 h-5" />
              </a>
            )}
            {social.email && (
              <a
                href={`mailto:${social.email}`}
                className="p-2 text-neutral-400 hover:text-neutral-600 transition-colors"
              >
                <ContactIcons.Email className="w-5 h-5" />
              </a>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  </motion.div>
);

export default function Team() {
  const params = useParams();
  const locale = params.locale as string;
  const localeTyped = locale as Locale;
  const isZh = localeTyped === 'zh';

  // 团队统计数据
  const stats = [
    { label: isZh ? '团队成员' : 'Team Members', value: '12+' },
    { label: isZh ? '项目经验' : 'Project Experience', value: '5年+' },
    { label: isZh ? '客户满意度' : 'Client Satisfaction', value: '98%' },
    { label: isZh ? '技术认证' : 'Certifications', value: '20+' },
  ];

  // 团队成员数据
  const members = [
    {
      name: isZh ? '张伟' : 'David Zhang',
      role: isZh ? '技术总监 & 全栈架构师' : 'CTO & Full-Stack Architect',
      avatar: '👨‍💻',
      bio: isZh
        ? '拥有8年+全栈开发经验，专注于高性能Web应用和微服务架构设计，曾主导多个大型项目的技术架构。'
        : 'With 8+ years of full-stack development experience, specializing in high-performance web applications and microservices architecture design.',
      skills: ['React', 'Node.js', 'TypeScript', 'AWS', 'Docker', 'Kubernetes'],
      experience: isZh ? '8年+ 全栈开发' : '8+ years Full-Stack',
      education: isZh ? '计算机科学硕士' : 'M.S. Computer Science',
      achievements: [
        isZh ? '主导设计了10+个大型项目架构' : 'Led architecture design for 10+ large projects',
        isZh ? '获得AWS解决方案架构师认证' : 'AWS Solutions Architect certified',
        isZh ? '开源项目贡献者，GitHub 1000+ stars' : 'Open source contributor, 1000+ GitHub stars'
      ],
      social: {
        github: 'https://github.com/davidzhang',
        linkedin: 'https://linkedin.com/in/davidzhang',
        email: '<EMAIL>'
      }
    },
    {
      name: isZh ? '李小雨' : 'Rain Li',
      role: isZh ? '前端技术专家' : 'Frontend Tech Lead',
      avatar: '👩‍💻',
      bio: isZh
        ? '专注于现代前端技术栈和用户体验设计，在React生态系统和性能优化方面有深入研究。'
        : 'Focused on modern frontend tech stack and UX design, with deep expertise in React ecosystem and performance optimization.',
      skills: ['React', 'Vue.js', 'TypeScript', 'Tailwind CSS', 'Framer Motion', 'Webpack'],
      experience: isZh ? '6年+ 前端开发' : '6+ years Frontend',
      education: isZh ? '软件工程学士' : 'B.S. Software Engineering',
      achievements: [
        isZh ? '优化项目性能提升50%+' : 'Optimized project performance by 50%+',
        isZh ? '设计了公司UI组件库' : 'Designed company UI component library',
        isZh ? '前端技术分享100+场次' : '100+ frontend tech sharing sessions'
      ],
      social: {
        github: 'https://github.com/rainli',
        linkedin: 'https://linkedin.com/in/rainli',
        email: '<EMAIL>'
      }
    },
    {
      name: isZh ? '王强' : 'Strong Wang',
      role: isZh ? '后端开发专家' : 'Backend Development Expert',
      avatar: '👨‍🔧',
      bio: isZh
        ? '在分布式系统和数据库优化方面有丰富经验，擅长高并发系统设计和微服务架构实现。'
        : 'Rich experience in distributed systems and database optimization, specializing in high-concurrency system design and microservices implementation.',
      skills: ['Java', 'Spring Boot', 'PostgreSQL', 'Redis', 'Kafka', 'Elasticsearch'],
      experience: isZh ? '7年+ 后端开发' : '7+ years Backend',
      education: isZh ? '计算机科学学士' : 'B.S. Computer Science',
      achievements: [
        isZh ? '设计支持百万级并发的系统' : 'Designed systems supporting millions of concurrent users',
        isZh ? '数据库性能优化专家' : 'Database performance optimization expert',
        isZh ? '获得Oracle认证数据库专家' : 'Oracle Certified Database Expert'
      ],
      social: {
        github: 'https://github.com/strongwang',
        linkedin: 'https://linkedin.com/in/strongwang',
        email: '<EMAIL>'
      }
    },
    {
      name: isZh ? '陈美丽' : 'Bella Chen',
      role: isZh ? 'UI/UX 设计师' : 'UI/UX Designer',
      avatar: '👩‍🎨',
      bio: isZh
        ? '专注于用户体验设计和视觉设计，擅长将复杂的业务需求转化为简洁优雅的用户界面。'
        : 'Focused on user experience and visual design, skilled at transforming complex business requirements into clean and elegant user interfaces.',
      skills: ['Figma', 'Sketch', 'Adobe Creative Suite', 'Prototyping', 'User Research', 'Design Systems'],
      experience: isZh ? '5年+ 设计经验' : '5+ years Design',
      education: isZh ? '视觉传达设计学士' : 'B.A. Visual Communication Design',
      achievements: [
        isZh ? '设计的产品获得红点设计奖' : 'Designed product won Red Dot Design Award',
        isZh ? '用户体验优化提升转化率30%+' : 'UX optimization improved conversion by 30%+',
        isZh ? '建立了完整的设计系统' : 'Established comprehensive design system'
      ],
      social: {
        linkedin: 'https://linkedin.com/in/bellachen',
        email: '<EMAIL>'
      }
    },
    {
      name: isZh ? '刘志明' : 'Ming Liu',
      role: isZh ? 'DevOps 工程师' : 'DevOps Engineer',
      avatar: '👨‍🔬',
      bio: isZh
        ? '专注于云原生技术和自动化部署，在容器化、CI/CD和基础设施即代码方面有丰富实践经验。'
        : 'Focused on cloud-native technologies and automated deployment, with rich practical experience in containerization, CI/CD, and infrastructure as code.',
      skills: ['Kubernetes', 'Docker', 'Terraform', 'Jenkins', 'AWS', 'Monitoring'],
      experience: isZh ? '6年+ DevOps' : '6+ years DevOps',
      education: isZh ? '网络工程学士' : 'B.S. Network Engineering',
      achievements: [
        isZh ? '部署自动化减少90%人工操作' : 'Deployment automation reduced manual work by 90%',
        isZh ? '系统可用性达到99.9%+' : 'System availability reached 99.9%+',
        isZh ? '获得Kubernetes认证管理员' : 'Certified Kubernetes Administrator'
      ],
      social: {
        github: 'https://github.com/mingliu',
        linkedin: 'https://linkedin.com/in/mingliu',
        email: '<EMAIL>'
      }
    },
    {
      name: isZh ? '赵敏' : 'Min Zhao',
      role: isZh ? '产品经理' : 'Product Manager',
      avatar: '👩‍💼',
      bio: isZh
        ? '拥有丰富的产品规划和项目管理经验，擅长需求分析和跨团队协作，确保项目按时高质量交付。'
        : 'Rich experience in product planning and project management, skilled in requirement analysis and cross-team collaboration.',
      skills: ['Product Strategy', 'Agile', 'Scrum', 'User Research', 'Data Analysis', 'Roadmapping'],
      experience: isZh ? '7年+ 产品管理' : '7+ years Product',
      education: isZh ? '工商管理硕士' : 'MBA',
      achievements: [
        isZh ? '成功交付50+个产品项目' : 'Successfully delivered 50+ product projects',
        isZh ? '产品用户满意度95%+' : 'Product user satisfaction 95%+',
        isZh ? '获得PMP项目管理认证' : 'PMP certified project manager'
      ],
      social: {
        linkedin: 'https://linkedin.com/in/minzhao',
        email: '<EMAIL>'
      }
    }
  ];

  // 团队价值观
  const values = [
    {
      icon: '🎯',
      title: isZh ? '专业专注' : 'Professional Focus',
      description: isZh
        ? '我们专注于技术深度，追求代码质量和工程卓越，每一行代码都体现我们的专业态度。'
        : 'We focus on technical depth, pursue code quality and engineering excellence, every line of code reflects our professional attitude.'
    },
    {
      icon: '🚀',
      title: isZh ? '持续创新' : 'Continuous Innovation',
      description: isZh
        ? '我们拥抱新技术，持续学习和改进，用创新的解决方案为客户创造更大价值。'
        : 'We embrace new technologies, continuously learn and improve, creating greater value for clients with innovative solutions.'
    },
    {
      icon: '🤝',
      title: isZh ? '团队协作' : 'Team Collaboration',
      description: isZh
        ? '我们相信团队的力量，通过开放沟通和相互支持，共同解决复杂的技术挑战。'
        : 'We believe in the power of teamwork, solving complex technical challenges through open communication and mutual support.'
    },
    {
      icon: '💡',
      title: isZh ? '客户成功' : 'Client Success',
      description: isZh
        ? '客户的成功就是我们的成功，我们致力于理解客户需求，提供超越期望的解决方案。'
        : 'Client success is our success. We are committed to understanding client needs and providing solutions that exceed expectations.'
    }
  ];

  return (
    <>
      {/* Hero 部分 */}
      <Hero
        title={isZh ? '我们的团队' : 'Our Team'}
        subtitle={isZh
          ? '一支充满激情的跨领域工程团队，用专业技能和创新思维为客户打造卓越的数字解决方案'
          : 'A passionate cross-disciplinary engineering team, creating exceptional digital solutions for clients with professional skills and innovative thinking'}
        align="center"
      />

      {/* 团队统计 */}
      <section className="py-16 bg-neutral-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
            variants={staggerContainer}
            className="grid md:grid-cols-4 gap-8"
          >
            {stats.map((stat, index) => (
              <motion.div
                key={index}
                variants={fadeInUp}
                className="text-center"
              >
                <div className="text-4xl font-bold text-primary-600 mb-2">{stat.value}</div>
                <div className="text-neutral-600">{stat.label}</div>
              </motion.div>
            ))}
          </motion.div>
        </div>
      </section>

      {/* 团队成员 */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
            variants={fadeInUp}
            className="text-center mb-16"
          >
            <h2 className="text-4xl font-semibold text-neutral-900 mb-6">
              {isZh ? '核心团队成员' : 'Core Team Members'}
            </h2>
            <p className="text-xl text-neutral-600 max-w-3xl mx-auto">
              {isZh
                ? '我们的团队由经验丰富的技术专家组成，每个人都在各自的领域有着深厚的专业积累'
                : 'Our team consists of experienced technical experts, each with deep professional expertise in their respective fields'}
            </p>
          </motion.div>

          <motion.div
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
            variants={staggerContainer}
            className="grid md:grid-cols-2 lg:grid-cols-3 gap-8"
          >
            {members.map((member, index) => (
              <ModernMemberCard
                key={member.name}
                {...member}
                locale={localeTyped}
              />
            ))}
          </motion.div>
        </div>
      </section>

      {/* 团队价值观 */}
      <section className="py-20 bg-neutral-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
            variants={fadeInUp}
            className="text-center mb-16"
          >
            <h2 className="text-4xl font-semibold text-neutral-900 mb-6">
              {isZh ? '我们的价值观' : 'Our Values'}
            </h2>
            <p className="text-xl text-neutral-600 max-w-3xl mx-auto">
              {isZh
                ? '这些核心价值观指导着我们的工作方式，塑造着我们的团队文化'
                : 'These core values guide our way of working and shape our team culture'}
            </p>
          </motion.div>

          <motion.div
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
            variants={staggerContainer}
            className="grid md:grid-cols-2 lg:grid-cols-4 gap-8"
          >
            {values.map((value, index) => (
              <motion.div
                key={index}
                variants={fadeInUp}
                className="text-center"
              >
                <div className="text-4xl mb-4">{value.icon}</div>
                <h3 className="text-xl font-semibold text-neutral-900 mb-3">
                  {value.title}
                </h3>
                <p className="text-neutral-600 leading-relaxed">
                  {value.description}
                </p>
              </motion.div>
            ))}
          </motion.div>
        </div>
      </section>

      {/* CTA 部分 */}
      <section className="py-20 bg-primary-600">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <motion.div
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
            variants={fadeInUp}
          >
            <h2 className="text-4xl font-semibold text-white mb-6">
              {isZh ? '想要加入我们的团队？' : 'Want to Join Our Team?'}
            </h2>
            <p className="text-xl text-primary-100 mb-8">
              {isZh
                ? '我们一直在寻找有才华、有激情的技术人才加入我们的团队'
                : 'We are always looking for talented and passionate technical professionals to join our team'}
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button
                variant="secondary"
                size="lg"
                onClick={() => window.location.href = `/${locale}/contact`}
                icon={NavigationIcons.Arrow}
                iconPosition="right"
              >
                {isZh ? '联系我们' : 'Contact Us'}
              </Button>
              <Button
                variant="outline"
                size="lg"
                onClick={() => window.location.href = `/${locale}/services`}
                className="border-white text-white hover:bg-white hover:text-primary-600"
              >
                {isZh ? '了解更多' : 'Learn More'}
              </Button>
            </div>
          </motion.div>
        </div>
      </section>
    </>
  );
}

