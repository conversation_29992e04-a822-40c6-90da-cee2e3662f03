#!/bin/bash

# 快速部署脚本 - 构建最新代码并重载PM2

echo "🚀 开始快速部署..."

# 1. 构建最新代码
echo "📦 构建项目..."
npm run build

if [ $? -ne 0 ]; then
    echo "❌ 构建失败，部署终止"
    exit 1
fi

# 2. 创建日志目录（如果不存在）
mkdir -p logs

# 3. 重载PM2服务
echo "🔄 重载PM2服务..."
pm2 reload ecosystem.config.js --env production

# 4. 显示状态
echo "✅ 部署完成！"
echo ""
echo "📊 当前PM2状态："
pm2 list

echo ""
echo "🌐 服务地址: http://localhost:55300"
echo "📝 查看日志: pm2 logs sanva-website"
