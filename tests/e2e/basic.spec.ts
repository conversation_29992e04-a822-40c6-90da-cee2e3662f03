import { test, expect } from '@playwright/test';

// 基础冒烟测试：验证关键页面能渲染且无严重错误
const locales = ['zh', 'en'] as const;

for (const locale of locales) {
  test.describe(`basic pages - ${locale}`, () => {
    test(`home page loads`, async ({ page }) => {
      const res = await page.goto(`/${locale}`);
      expect(res?.ok()).toBeTruthy();
      await expect(page.locator('header')).toBeVisible();
      await expect(page.locator('footer')).toBeVisible();
    });

    const paths = ['about', 'services', 'portfolio', 'contact', 'blog', 'team', 'pricing', 'case-studies'];
    for (const p of paths) {
      test(`${p} page loads`, async ({ page }) => {
        const res = await page.goto(`/${locale}/${p}`);
        expect(res?.ok()).toBeTruthy();
        await expect(page.locator('main')).toBeVisible();
      });
    }
  });
}

