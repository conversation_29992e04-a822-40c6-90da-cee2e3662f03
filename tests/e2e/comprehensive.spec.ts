import { test, expect } from '@playwright/test';

// 综合测试：验证网站的整体功能和用户体验
const locales = ['zh', 'en'] as const;

for (const locale of locales) {
  test.describe(`Comprehensive tests - ${locale}`, () => {
    test.beforeEach(async ({ page }) => {
      await page.goto(`/${locale}`);
      await expect(page.locator('header')).toBeVisible();
    });

    test('Complete user journey - browse all pages', async ({ page }) => {
      // 验证首页加载
      await expect(page.locator('h1')).toBeVisible();
      
      // 测试所有主要页面的导航
      const pages = [
        { name: 'about', path: `/${locale}/about` },
        { name: 'services', path: `/${locale}/services` },
        { name: 'portfolio', path: `/${locale}/portfolio` },
        { name: 'blog', path: `/${locale}/blog` },
        { name: 'team', path: `/${locale}/team` },
        { name: 'case-studies', path: `/${locale}/case-studies` },
        { name: 'pricing', path: `/${locale}/pricing` },
        { name: 'contact', path: `/${locale}/contact` },
      ];

      for (const pageInfo of pages) {
        console.log(`Testing navigation to ${pageInfo.name} page`);
        
        // 点击导航链接
        const navLink = page.locator(`header nav a[href="${pageInfo.path}"]`);
        await expect(navLink).toBeVisible();
        await navLink.click();
        
        // 等待页面加载 - 增加超时时间
        await page.waitForURL(`**${pageInfo.path}`, { timeout: 30000 });
        await page.waitForLoadState('networkidle', { timeout: 30000 });
        
        // 验证页面内容
        await expect(page.locator('main')).toBeVisible();

        // 验证页面有标题（h1或h2）
        const hasH1 = await page.locator('h1').count() > 0;
        const hasH2 = await page.locator('h2').count() > 0;
        expect(hasH1 || hasH2).toBeTruthy();
        
        // 验证没有错误
        const errorCount = await page.locator('text=/error|Error|错误|404|500/i').count();
        expect(errorCount).toBe(0);
        
        // 验证页面标题
        const title = await page.title();
        expect(title).toBeTruthy();
        expect(title.length).toBeGreaterThan(0);
      }
    });

    test('Header and footer elements are present', async ({ page }) => {
      // 验证头部元素
      await expect(page.locator('header')).toBeVisible();
      await expect(page.locator('header nav')).toBeVisible();
      
      // 验证Logo/品牌名称
      const brandLink = page.locator('header a').first();
      await expect(brandLink).toBeVisible();
      
      // 验证语言切换按钮 - 使用更精确的选择器
      const langButton = page.locator('header nav button').filter({ hasText: locale === 'zh' ? '🇨🇳' : '🇺🇸' }).first();
      await expect(langButton).toBeVisible();
      
      // 验证页脚
      await expect(page.locator('footer')).toBeVisible();
      
      // 验证页脚链接
      const footerLinks = page.locator('footer a');
      const linkCount = await footerLinks.count();
      expect(linkCount).toBeGreaterThan(0);
    });

    test('Responsive design works', async ({ page }) => {
      // 测试桌面版
      await page.setViewportSize({ width: 1200, height: 800 });
      await expect(page.locator('header nav')).toBeVisible();
      
      // 测试平板版
      await page.setViewportSize({ width: 768, height: 1024 });
      await page.waitForTimeout(500); // 等待响应式变化
      
      // 测试移动版
      await page.setViewportSize({ width: 375, height: 667 });
      await page.waitForTimeout(500);
      
      // 验证移动端菜单按钮存在
      const mobileMenuButton = page.locator('header button[aria-expanded]');
      await expect(mobileMenuButton).toBeVisible();
      
      // 测试移动端菜单
      await mobileMenuButton.click();
      const mobileMenu = page.locator('#mobile-menu');
      await expect(mobileMenu).toBeVisible();
    });

    test('Contact form elements exist', async ({ page }) => {
      // 导航到联系页面
      await page.goto(`/${locale}/contact`);
      await page.waitForLoadState('networkidle');
      
      // 验证页面加载
      await expect(page.locator('main')).toBeVisible();
      
      // 检查是否有表单元素（如果存在）
      const forms = await page.locator('form').count();
      const inputs = await page.locator('input, textarea').count();
      
      // 如果有表单，验证基本元素
      if (forms > 0) {
        await expect(page.locator('form')).toBeVisible();
      }
      
      // 验证联系信息存在
      const contactInfo = page.locator('text=/email|邮箱|contact|联系/i');
      const contactCount = await contactInfo.count();
      expect(contactCount).toBeGreaterThan(0);
    });

    test('All images load properly', async ({ page }) => {
      // 等待页面完全加载
      await page.waitForLoadState('networkidle');
      
      // 获取所有图片
      const images = page.locator('img');
      const imageCount = await images.count();
      
      if (imageCount > 0) {
        // 检查每个图片是否加载成功
        for (let i = 0; i < Math.min(imageCount, 10); i++) { // 限制检查前10个图片
          const img = images.nth(i);
          const src = await img.getAttribute('src');
          
          if (src && !src.startsWith('data:')) { // 跳过base64图片
            // 验证图片可见
            await expect(img).toBeVisible();
          }
        }
      }
    });

    test('Page performance is acceptable', async ({ page }) => {
      const startTime = Date.now();
      
      // 导航到首页并等待加载完成
      await page.goto(`/${locale}`);
      await page.waitForLoadState('networkidle');
      
      const loadTime = Date.now() - startTime;
      
      // 验证页面在合理时间内加载（10秒内）
      expect(loadTime).toBeLessThan(10000);
      
      // 验证页面内容存在
      await expect(page.locator('main')).toBeVisible();
      await expect(page.locator('h1')).toBeVisible();
    });
  });
}
