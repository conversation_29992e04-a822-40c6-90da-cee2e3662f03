module.exports = {
  apps: [
    {
      name: 'sanva-website',
      script: 'node_modules/next/dist/bin/next',
      args: 'start',
      instances: 'max', // 使用所有可用CPU核心
      exec_mode: 'cluster', // 启用集群模式
      watch: false, // 不监视文件变化
      max_memory_restart: '1G', // 内存超过1G时自动重启
      env: {
        NODE_ENV: 'production',
        PORT: 55300,
        HOST: '0.0.0.0'
      },
      log_date_format: 'YYYY-MM-DD HH:mm:ss',
      error_file: './logs/error.log',
      out_file: './logs/out.log',
      merge_logs: true,
      autorestart: true, // 自动重启
      restart_delay: 4000, // 重启延迟
      wait_ready: false, // 等待应用发送ready信号
      listen_timeout: 50000, // 监听超时
      pre_deploy: "yarn build" // 添加构建脚本
    }
  ]
}; 