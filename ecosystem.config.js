module.exports = {
  apps: [
    {
      name: 'sanva-website',
      script: 'node_modules/next/dist/bin/next',
      args: 'start',
      instances: 2, // 明确指定2个实例，更可控 - 优化配置
      exec_mode: 'cluster', // 启用集群模式
      watch: false, // 不监视文件变化
      max_memory_restart: '800M', // 降低内存限制，更早重启 - 优化配置
      min_uptime: '10s', // 最小运行时间，避免频繁重启 - 新增配置
      max_restarts: 10, // 最大重启次数限制 - 新增配置
      env: {
        NODE_ENV: 'production',
        PORT: 55300,
        HOST: '0.0.0.0'
      },
      env_staging: {
        NODE_ENV: 'staging',
        PORT: 55301,
        HOST: '0.0.0.0'
      },
      log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
      error_file: './logs/error.log',
      out_file: './logs/out.log',
      log_file: './logs/combined.log', // 新增合并日志 - 优化配置
      merge_logs: true,
      autorestart: true, // 自动重启
      restart_delay: 4000, // 重启延迟
      wait_ready: false, // 等待应用发送ready信号
      listen_timeout: 50000, // 监听超时
      kill_timeout: 5000, // 强制杀死超时 - 新增配置
      pre_deploy: "yarn build", // 添加构建脚本
      // 健康检查配置 - 新增
      health_check_grace_period: 3000,
      // 实例间负载均衡配置 - 新增
      instance_var: 'INSTANCE_ID'
    }
  ],

  // 部署配置 - 新增完整部署配置
  deploy: {
    production: {
      user: 'deploy',
      host: ['your-server.com'],
      ref: 'origin/main',
      repo: '**************:your-username/sanva_website.git',
      path: '/var/www/sanva-website',
      'pre-deploy-local': '',
      'post-deploy': 'yarn install && yarn build && pm2 reload ecosystem.config.js --env production',
      'pre-setup': ''
    }
  }
};