#!/bin/bash

# 快速修复 @next/font 问题的脚本

echo "🔧 修复 @next/font 依赖问题..."

# 1. 移除旧的 @next/font 包
echo "🗑️ 移除旧的 @next/font 包..."
npm uninstall @next/font

# 2. 清理 package-lock.json 和 node_modules
echo "🧹 清理依赖..."
rm -f package-lock.json
rm -rf node_modules

# 3. 重新安装依赖
echo "📦 重新安装依赖..."
npm install

# 4. 测试构建
echo "🏗️ 测试构建..."
npm run build

if [ $? -eq 0 ]; then
    echo "✅ @next/font 问题已修复！"
    echo "✅ 现在使用的是 Next.js 15 内置的字体功能"
else
    echo "❌ 构建失败，请检查其他问题"
    exit 1
fi

echo "🎉 修复完成！"
