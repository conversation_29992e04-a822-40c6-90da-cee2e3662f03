#!/bin/bash

# 三娃软件网站 PM2 部署脚本
# 功能：构建最新代码并部署到生产环境

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查必要的命令
check_dependencies() {
    log_info "检查依赖..."
    
    if ! command -v node &> /dev/null; then
        log_error "Node.js 未安装"
        exit 1
    fi
    
    if ! command -v npm &> /dev/null; then
        log_error "npm 未安装"
        exit 1
    fi
    
    if ! command -v pm2 &> /dev/null; then
        log_error "PM2 未安装，正在安装..."
        npm install -g pm2
    fi
    
    log_success "依赖检查完成"
}

# 拉取最新代码
pull_latest_code() {
    log_info "拉取最新代码..."
    
    # 检查是否有未提交的更改
    if ! git diff-index --quiet HEAD --; then
        log_warning "检测到未提交的更改，正在暂存..."
        git stash push -m "Auto stash before deploy $(date)"
    fi
    
    # 拉取最新代码
    git fetch origin
    git pull origin main
    
    log_success "代码更新完成"
}

# 安装依赖
install_dependencies() {
    log_info "安装/更新依赖..."

    # 强制重新安装依赖以确保平台特定模块正确安装
    log_info "清理旧依赖..."
    rm -rf node_modules package-lock.json

    # 重新安装依赖
    log_info "重新安装依赖..."
    npm install

    # 验证关键依赖
    if [ ! -d "node_modules/@tailwindcss" ]; then
        log_error "Tailwind CSS 安装失败"
        exit 1
    fi

    log_success "依赖安装完成"
}

# 构建项目
build_project() {
    log_info "构建项目..."
    
    # 清理之前的构建
    rm -rf .next
    
    # 构建项目
    npm run build
    
    if [ $? -eq 0 ]; then
        log_success "项目构建成功"
    else
        log_error "项目构建失败"
        exit 1
    fi
}

# 创建日志目录
create_log_directory() {
    if [ ! -d "logs" ]; then
        log_info "创建日志目录..."
        mkdir -p logs
        log_success "日志目录创建完成"
    fi
}

# 部署到PM2
deploy_to_pm2() {
    log_info "部署到 PM2..."
    
    # 检查PM2进程是否存在
    if pm2 list | grep -q "sanva-website"; then
        log_info "检测到现有进程，执行重载..."
        pm2 reload ecosystem.config.js --env production
    else
        log_info "启动新的PM2进程..."
        pm2 start ecosystem.config.js --env production
    fi
    
    # 保存PM2配置
    pm2 save
    
    log_success "PM2 部署完成"
}

# 健康检查
health_check() {
    log_info "执行健康检查..."
    
    # 等待服务启动
    sleep 5
    
    # 检查PM2状态
    pm2 list
    
    # 检查服务是否响应
    if curl -f http://localhost:55300 > /dev/null 2>&1; then
        log_success "服务健康检查通过"
    else
        log_warning "服务可能未完全启动，请检查日志"
    fi
}

# 显示部署信息
show_deploy_info() {
    echo ""
    log_success "=== 部署完成 ==="
    echo -e "${BLUE}服务地址:${NC} http://localhost:55300"
    echo -e "${BLUE}PM2 状态:${NC} pm2 list"
    echo -e "${BLUE}查看日志:${NC} pm2 logs sanva-website"
    echo -e "${BLUE}重启服务:${NC} pm2 restart sanva-website"
    echo -e "${BLUE}停止服务:${NC} pm2 stop sanva-website"
    echo ""
}

# 主函数
main() {
    log_info "开始部署三娃软件网站..."
    echo ""
    
    check_dependencies
    pull_latest_code
    install_dependencies
    build_project
    create_log_directory
    deploy_to_pm2
    health_check
    show_deploy_info
    
    log_success "部署流程全部完成！"
}

# 错误处理
trap 'log_error "部署过程中发生错误，请检查日志"; exit 1' ERR

# 执行主函数
main "$@"
